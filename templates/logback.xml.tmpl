<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-llm-integration-service.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/heal-llm-integration-service_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/llmintegrationservice/logs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/llmintegrationservice/logs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/llmintegrationservice/logs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-llm-integration-service-core.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/heal-llm-integration-service-core_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/llmintegrationservice/corelogs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/llmintegrationservice/corelogs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/llmintegrationservice/corelogs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/heal-llm-integration-service-stats.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/heal-llm-integration-service-stats_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/llmintegrationservice/auditlogs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/llmintegrationservice/auditlogs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/llmintegrationservice/auditlogs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.heal.integration.service" level="{{ key "service/llmintegrationservice/loglevel" }}" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.heal.integration.service.scheduler" level="{{ key "service/llmintegrationservice/statsloglevel" }}" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <root level="{{ key "service/llmintegrationservice/rootloglevel" }}">
        <appender-ref ref="CORE_FILE"/>
    </root>
</configuration>
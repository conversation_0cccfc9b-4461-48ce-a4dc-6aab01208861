# ==========================================================
# Keycloak config
# ==========================================================
keycloak.ip={{ key "service/keycloak/hostname" }}
keycloak.port={{ key "service/keycloak/port/https" }}
keycloak.user={{ key "service/keycloak/username" }}
keycloak.pwd={{ key "service/keycloak/password" }}

# ==========================================================
# RabbitMQ server configuration
# Basic configuration needed to communicate with RabbitMQ.
# HTTP mode of communication will be utilized in this case.
# ==========================================================
spring.rabbitmq.addresses={{ key "service/rabbitmq/addresses" }}
spring.rabbitmq.username={{ key "service/rabbitmq/username" }}
spring.rabbitmq.password={{ key "service/rabbitmq/password/encrypted" }}
spring.rabbitmq.ssl.enabled={{ key "service/rabbitmq/sslenable" }}
spring.rabbitmq.ssl.algorithm={{ key "service/rabbitmq/sslprotocol" }}
spring.rabbitmq.signalMessageAIOpsQueueName={{ key "service/rabbitmq/signalmessageaiopsqueue" }}
spring.rabbitmq.signalMessageAIOps.prefetchCount={{ key "service/llmintegrationservice/rabbitmq/signalmessageaiops/prefetchCount" }}
spring.rabbitmq.signalMessageAIOps.acknowledgementMode={{ key "service/llmintegrationservice/rabbitmq/signalmessageaiops/ackMode" }}
spring.rabbitmq.signalMessageAIOps.concurrentConsumerSize={{ key "service/llmintegrationservice/rabbitmq/signalmessageaiops/consumerSize"}}

# ==========================================================
# Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes={{ key "service/redis/nodes" }}
spring.redis.ssl={{ key "service/redis/sslenable" }}
spring.redis.username={{ key "service/redis/username" }}
spring.redis.password={{ key "service/redis/password/encrypted" }}
spring.redis.cluster.mode={{ key "service/redis/cluster/mode" }}
spring.redis.max.idle.connections={{ key "service/llmintegrationservice/redis/max/idle/connection" }}
spring.redis.min.idle.connections={{ key "service/llmintegrationservice/redis/min/idle/connection" }}
spring.redis.max.total.connections={{ key "service/llmintegrationservice/redis/max/total/connection" }}
spring.redis.max.wait.secs={{ key "service/llmintegrationservice/redis/max/wait/secs" }}
spring.redis.share.native.connection={{ key "service/llmintegrationservice/redis/share/native/connection" }}

# ===============================================================
# Local Cache Configuration
# ===============================================================
redis.cache.mode={{ key "service/llmintegrationservice/redis/cache/mode" }}
topology.configuration.cache.max.size={{ key "service/llmintegrationservice/topology/config/cache/max/size" }}
topology.configuration.cache.expire.interval.minutes={{ key "service/llmintegrationservice/topology/config/cache/expire/interval/minute" }}
masterData.configuration.cache.max.size={{ key "service/llmintegrationservice/masterData/config/cache/max/size" }}
masterData.configuration.cache.expire.interval.minutes={{ key "service/llmintegrationservice/masterData/config/cache/expire/interval/minute" }}
account.configuration.cache.max.size={{ key "service/llmintegrationservice/account/config/cache/max/size" }}
account.configuration.cache.expire.interval.minutes={{ key "service/llmintegrationservice/account/config/cache/expire/interval/minute" }}
incident.profile.configuration.cache.max.size={{ key "service/llmintegrationservice/incident/profile/config/cache/max/size" }}
incident.profile.configuration.cache.expire.interval.minutes={{ key "service/llmintegrationservice/incident/profile/config/cache/expire/interval/minute" }}

# ==========================================================
# Opensearch Server Configuration
# ==========================================================
opensearch.connection.io.reactor.size={{ key "service/llmintegrationservice/opensearch/connection/io/reactor/size" }}
opensearch.batch.size={{key "service/llmintegrationservice/opensearch/batch/size" }}
opensearch.batch.queue.max.size={{key "service/llmintegrationservice/opensearch/batch/queue/max/size" }}
opensearch.data.push.schedule.interval={{key "service/llmintegrationservice/opensearch/data/push/schedule/interval/secs" }}
opensearch.data.push.schedule.initial.delay={{key "service/llmintegrationservice/opensearch/data/push/schedule/initial/delay/secs" }}

# ==========================================================
# Thread Pool Configuration
# ==========================================================
thread.pool.queue.capacity={{ key "service/llmintegrationservice/thread/pool/queue/capacity" }}

# ==========================================================
# LLM Details
# ==========================================================
llm.url={{ key "service/llmintegrationservice/llm/url" }}
llm.criteria.suppress.per.incident.min={{ key "service/llmintegrationservice/llm/criteria/suppress/per/incident/min" }}
llm.suppress.api.per.min={{ key "service/llmintegrationservice/llm/suppress/api/per/min" }}
llm.rest.api.pusher.schedule.initial.delay={{ key "service/llmintegrationservice/llm/rest/api/pusher/schedule/initial/delay" }}
llm.rest.api.pusher.schedule.interval={{ key "service/llmintegrationservice/llm/rest/api/pusher/schedule/interval" }}
llm.rest.api.pusher.max.queue.size={{ key "service/llmintegrationservice/llm/rest/api/pusher/max/queue/size" }}

# ==========================================================
# LLM-IS TOMCAT Server Details
# ==========================================================
server.port={{ key "service/llmintegrationservice/integration/server/port" }}
server.ssl.key-store={{key "service/llmintegrationservice/server/ssl/keystorepath" }}
server.ssl.key-store-type={{key "service/llmintegrationservice/server/ssl/keystoretype" }}
server.ssl.key-store-password={{key "service/llmintegrationservice/server/ssl/keystorepassword" }}
server.ssl.trust.store={{key "service/llmintegrationservice/server/ssl/truststorepath" }}
server.ssl.trust.store.password={{key "service/llmintegrationservice/server/ssl/truststorepassword" }}
http.connect.timeout.sec={{ key "service/llmintegrationservice/http/connect/timeout/sec" }}
http.read.timeout.sec={{ key "service/llmintegrationservice/http/read/timeout/sec" }}

# ==========================================================
# Health Metrics Details
# ==========================================================
health.metrics.update.interval.milliseconds={{key "service/llmintegrationservice/health/metrics/updateinterval/milliseconds" }}
health.metrics.log.interval.milliseconds={{key "service/llmintegrationservice/health/metrics/loginterval/milliseconds" }}
management.endpoints.web.exposure.include={{key "service/llmintegrationservice/management/endpoints/web/exposure/include" }}
management.endpoints.jmx.exposure.include={{key "service/llmintegrationservice/management/endpoints/jmx/exposure/include" }}
management.endpoint.health.enabled={{key "service/llmintegrationservice/management/endpoints/health/enabled" }}
management.endpoints.web.base-path=/measure
management.server.port={{key "service/llmintegrationservice/management/server/port" }}
spring.jmx.enabled={{ key "service/llmintegrationservice/jmx/enabled" }}

# ==========================================================
# Others
# ==========================================================
fetch.os.closed.signals.interval.days={{ key "service/llmintegrationservice/fetch/os/closed/signals/interval/days" }}
redis.delete.closed.signal.scheduler.interval.seconds={{ key "service/llmintegrationservice/redis/delete/closed/signal/scheduler/interval/seconds"}}
redis.delete.closed.signal.scheduler.initial.delay.seconds={{ key "service/llmintegrationservice/redis/delete/closed/signal/scheduler/initial/delay/seconds"}}
minimum.service.count={{ key "service/llmintegrationservice/minimum/service/count/criteria/match"}}
maximum.questionandanswer.os={{key "service/llmintegrationservice/maximum/questionandanswer/os"}}
prompt.message.system.role={{key "service/llmintegrationservice/prompt/message/system/role"}}
http.blocked.methods={{key "service/llmintegrationservice/blocked/http/methods"}}
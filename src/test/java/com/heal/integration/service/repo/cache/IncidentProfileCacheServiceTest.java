package com.heal.integration.service.repo.cache;

import com.heal.integration.service.config.PropertyConfig;
import com.heal.integration.service.pojo.profiling.AccountProfile;
import com.heal.integration.service.pojo.profiling.Categories;
import com.heal.integration.service.pojo.profiling.IncidentProfile;
import com.heal.integration.service.pojo.profiling.Services;
import com.heal.integration.service.repo.RedisRepo;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * <AUTHOR> Suman - 10-07-2024
 */
@SpringBootTest
public class IncidentProfileCacheServiceTest {

    @Mock
    private PropertyConfig propertyConfig;
    @Mock
    private CacheManager cacheManager;

    private IncidentProfileCacheService incidentProfileCacheService;
    @Mock
    private RedisRepo redisRepo;


    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        incidentProfileCacheService = new IncidentProfileCacheService(propertyConfig, cacheManager, redisRepo);
    }

    @After
    public void tearDown() throws Exception {
    }

    @Test
    public void testUpdateServices_ProfileChanged() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setServices(Services.builder().count(1).names(new HashSet<String>() {{
            add("service1");
        }}).build());
        accountCacheMap.put("account1", cachedAccount);

        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account1");
        newAccount.setServices(new Services(1, new HashSet<String>() {{
            add("service2");
            add("service1");
        }}));
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateServices(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertTrue(result);
        assertTrue(criteriaDetails.toString().contains("Number of services changed"));
        assertEquals(2, accountCacheMap.get("account1").getServices().getNames().size());
    }

    @Test
    public void testUpdateServices_ProfileUnchanged() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setServices(new Services(1, new HashSet<String>() {{
            add("service1");
        }}));
        accountCacheMap.put("account1", cachedAccount);

        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account1");
        newAccount.setServices(new Services(1, new HashSet<String>() {{
            add("service1");
        }}));
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateServices(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(1, accountCacheMap.get("account1").getServices().getNames().size());
    }

    @Test
    public void testUpdateServices_ProfileUnchangedForDifferentAccount() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setServices(new Services(1, new HashSet<String>() {{
            add("service1");
        }}));
        accountCacheMap.put("account1", cachedAccount);

        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account2");
        newAccount.setServices(new Services(1, new HashSet<String>() {{
            add("service1");
        }}));
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateServices(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(1, accountCacheMap.get("account1").getServices().getNames().size());
    }

    @Test
    public void testUpdateServices_EmptyAccountCacheMap() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();

        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account1");
        newAccount.setServices(new Services(1, new HashSet<String>() {{
            add("service1");
        }}));
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateServices(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(0, accountCacheMap.size());
    }

    @Test
    public void testUpdateServices_NullProfile() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateServices(accountCacheMap, null, criteriaDetails);

        // Assert
        assertFalse(result);
        assertTrue(criteriaDetails.length() == 0);
    }

    @Test
    public void testUpdateServices_EmptyProfile() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.emptyList());
        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateServices(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertTrue(criteriaDetails.length() == 0);
    }

    @Test
    public void testUpdateCategories_ProfileChanged() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setCategories(new Categories(1, new HashSet<String>() {{
            add("category1");
        }}));
        accountCacheMap.put("account1", cachedAccount);

        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account1");
        newAccount.setCategories(new Categories(1, new HashSet<String>() {{
            add("category2");
        }}));
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateCategories(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertTrue(result);
        assertTrue(criteriaDetails.toString().contains("Number of categories changed"));
        assertEquals(2, accountCacheMap.get("account1").getCategories().getNames().size());
    }

    @Test
    public void testUpdateCategories_ProfileUnchanged() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setCategories(new Categories(1, new HashSet<String>() {{
            add("category1");
        }}));
        accountCacheMap.put("account1", cachedAccount);

        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account1");
        newAccount.setCategories(new Categories(1, new HashSet<String>() {{
            add("category1");
        }}));
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateCategories(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(1, accountCacheMap.get("account1").getCategories().getNames().size());
    }

    @Test
    public void testUpdateCategories_ProfileUnchangedForDifferentAccount() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setCategories(new Categories(1, new HashSet<String>() {{
            add("category1");
        }}));
        accountCacheMap.put("account1", cachedAccount);

        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account2");
        newAccount.setCategories(new Categories(1, new HashSet<String>() {{
            add("category1");
        }}));
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateCategories(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(1, accountCacheMap.get("account1").getCategories().getNames().size());
    }

    @Test
    public void testUpdateCategories_CacheProfileNull() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = null;
        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account1");
        newAccount.setCategories(new Categories(1, new HashSet<String>() {{
            add("category1");
        }}));
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateCategories(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(0, criteriaDetails.length());
    }

    @Test
    public void testUpdateCategories_NewProfileNull() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setCategories(new Categories(1, new HashSet<String>() {{
            add("category1");
        }}));
        accountCacheMap.put("account1", cachedAccount);

        IncidentProfile newProfile = null;
        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateCategories(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(0, criteriaDetails.length());
    }

    @Test
    public void testUpdateSeverity_SeverityChanged() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setSeverity(2);
        accountCacheMap.put("account1", cachedAccount);

        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account1");
        newAccount.setSeverity(3);
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateSeverity(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertTrue(result);
        assertTrue(criteriaDetails.toString().contains("Severity changed"));
        assertEquals(3, accountCacheMap.get("account1").getSeverity());
    }

    @Test
    public void testUpdateSeverity_SeverityUnchanged() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setSeverity(2);
        accountCacheMap.put("account1", cachedAccount);

        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account1");
        newAccount.setSeverity(2);
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateSeverity(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(2, accountCacheMap.get("account1").getSeverity());
        assertEquals(0, criteriaDetails.length());
    }

    @Test
    public void testUpdateSeverity_CacheProfileNull() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = null;
        AccountProfile newAccount = new AccountProfile();
        newAccount.setAccountId("account1");
        newAccount.setSeverity(3);
        IncidentProfile newProfile = new IncidentProfile("Incident-1", 1720615149000L, Collections.singletonList(newAccount));

        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateSeverity(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(0, criteriaDetails.length());
    }

    @Test
    public void testUpdateSeverity_NewProfileNull() {
        // Arrange
        Map<String, AccountProfile> accountCacheMap = new HashMap<>();
        AccountProfile cachedAccount = new AccountProfile();
        cachedAccount.setAccountId("account1");
        cachedAccount.setSeverity(2);
        accountCacheMap.put("account1", cachedAccount);

        IncidentProfile newProfile = null;
        StringBuilder criteriaDetails = new StringBuilder();

        // Act
        boolean result = incidentProfileCacheService.updateSeverity(accountCacheMap, newProfile, criteriaDetails);

        // Assert
        assertFalse(result);
        assertEquals(0, criteriaDetails.length());
    }

    @Test
    public void getLastCacheIncidentProfile() {
    }

    @Test
    public void updateIncidentProfile() {
    }

    @Test
    public void isTimeDifferenceGreaterThanNMinutes() {
    }

    @Test
    public void getLastLLMCallTime() {
    }

    @Test
    public void isIncidentProfileCached() {
    }

    @Test
    public void updateCategories() {
    }

    @Test
    public void updateSeverity() {
    }
}
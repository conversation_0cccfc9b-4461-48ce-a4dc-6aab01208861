package com.heal.integration.service;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> <PERSON>man - 02-04-2024
 */

class HealIntegrationServiceApplicationTest {
    public static void main(String[] args) {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost("http://192.168.13.101:8899/api/v1.0/conversation");
            httpPost.setEntity(new StringEntity("{  \"userId\": \"7640123a-fbde-4fe5-9812-581cd1e3a9c1\",  " +
                    "\"accountIdentifier\": \"ubi\",  \"incidentIdentifier\": \"inc3\",  \"uuid\": \"uuid2\",  " +
                    "\"conversationId\": \"conv2\",  \"conversationTitle\": \"title2\",  \"conversationStartTime\": *************,  \"isNewConversation\": false,  \"questionId\": \"qid1\",  \"question\": \"ques3\",  \"questionTime\": *************}"));
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setHeader("Authorization", "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ6QU1jSkt2dk92MWIyTmNleUJBOW5ZZkV2SnN6RHpJZEZLTVhVUHY1N09vIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BHjoa3jFBj4pNzeg-1QcLeewWctJzE2ewEsnuCHD8SM1B5oOU0JecAbVqHdo2IAtf7nkY6gimG3UItLEW1qZBfUs1vwSLb2_5Bns1Da8Nu8lOG2_Lw3ZlyOknZ3-HVyMLkAGt1vCimFeE3_w9IqFHe7bNKYhItwhNf-r6tHilISIWsTo43G-v0eVJXfz0ZDNbVNu6zpzi28O41NiYkEJ-i4hPjrFvjV_X8nVoLKNX4dhVDDbUVSHypjcJEuDB1h3DeVyZWSq8NRTFInNanoPIfUkba2lj6jczYZ9PAWs4sIaThSD9WEFlO0p1Xi_o7cb0nXVAfZx5UfqW8sCGV9MWQ");
            CloseableHttpResponse response = client.execute(httpPost);
            System.out.println(EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8));
            System.out.println(response.getStatusLine().getStatusCode());
            client.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
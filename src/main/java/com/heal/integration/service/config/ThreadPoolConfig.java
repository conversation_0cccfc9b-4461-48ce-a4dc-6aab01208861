package com.heal.integration.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Slf4j
@Configuration
public class ThreadPoolConfig {

    private final PropertyConfig propertyConfig;

    public ThreadPoolConfig(PropertyConfig propertyConfig) {
        this.propertyConfig = propertyConfig;
    }

    @Bean("threadPoolTaskExecutor")
    public ThreadPoolTaskExecutor getAsyncExecutor() {
        log.info("Setting up thread executor");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(propertyConfig.getConcurrentConsumerSize());
        executor.setMaxPoolSize(propertyConfig.getConcurrentConsumerSize());
        executor.setThreadNamePrefix("LLM-IS-Worker-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(10);
        executor.setQueueCapacity(propertyConfig.getQueueCapacity());
        executor.initialize();

        return executor;
    }
}


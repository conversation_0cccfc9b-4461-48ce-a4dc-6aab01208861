package com.heal.integration.service.config.cache;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CacheConstants {
    public static final String TOPOLOGY_CACHE = "topologyCache";
    public static final String MASTER_DATA_CACHE = "masterCache";
    public static final String ACCOUNT_CACHE = "accountCache";
    public static final String INCIDENT_PROFILE_CACHE = "incidentProfileCache";
}

package com.heal.integration.service.config;

import com.heal.integration.service.processor.EventConsumerFromQueue;
import com.heal.integration.service.utility.CommonUtils;
import com.heal.integration.service.utility.Constants;
import com.heal.integration.service.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.RabbitConnectionFactoryBean;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Objects;

@Configuration
@Slf4j
public class RabbitMQConfig {

    private final EventConsumerFromQueue receiver;
    private final HealthMetrics healthMetrics;
    private final CommonUtils commonUtils;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final PropertyConfig propertyConfig;

    public RabbitMQConfig(EventConsumerFromQueue receiver, HealthMetrics healthMetrics, CommonUtils commonUtils,
                          @Qualifier("threadPoolTaskExecutor") ThreadPoolTaskExecutor threadPoolTaskExecutor,
                          PropertyConfig propertyConfig) {
        this.receiver = receiver;
        this.healthMetrics = healthMetrics;
        this.commonUtils = commonUtils;
        this.threadPoolTaskExecutor = threadPoolTaskExecutor;
        this.propertyConfig = propertyConfig;
    }

    @Qualifier("signalMessageAIOpsInputQueue")
    @Bean
    Queue createSignalMessageAIOpsInputQueue() {
        healthMetrics.setReadQueueName(propertyConfig.signalMessageAIOpsQueueName);
        return new Queue(propertyConfig.signalMessageAIOpsQueueName, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Bean
    ConnectionFactory connectionFactory(RabbitProperties rabbitProperties) {
        RabbitConnectionFactoryBean factory = new RabbitConnectionFactoryBean();

        if (rabbitProperties.getUsername() != null) {
            factory.setUsername(rabbitProperties.getUsername());
        }
        if (rabbitProperties.getPassword() != null) {
            factory.setPassword(commonUtils.getDecryptedData(rabbitProperties.getPassword().trim()));
        }

        RabbitProperties.Ssl ssl = rabbitProperties.getSsl();
        if (ssl.getEnabled()) {
            factory.setUseSSL(true);
            factory.setEnableHostnameVerification(false);
            factory.setSslAlgorithm(ssl.getAlgorithm());
        }
        factory.setAutomaticRecoveryEnabled(true);
        factory.afterPropertiesSet();

        CachingConnectionFactory connectionFactory = null;
        try {
            connectionFactory = new CachingConnectionFactory(Objects.requireNonNull(factory.getRabbitConnectionFactory()));
            connectionFactory.setAddresses(rabbitProperties.getAddresses());
        } catch (Exception e) {
            healthMetrics.updateLLMErrors();
            log.error("Exception occurred while creating ConnectionFactory. " +
                    "Details: Addresses:{}, SSL:{} ", rabbitProperties.getAddresses(), rabbitProperties.getSsl(), e);
        }
        return connectionFactory;
    }

    @Qualifier("signalMessageAIOpsDataContainer")
    @Bean
    SimpleMessageListenerContainer signalMessageAIOpsContainer(ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(propertyConfig.getSignalMessageAIOpsQueueName());
        container.setMessageListener(new MessageListenerAdapter(receiver, "receiveSignalMessageAIOpsData"));
        container.setAcknowledgeMode(AcknowledgeMode.valueOf(propertyConfig.getAcknowledgementMode()));
        container.setConcurrentConsumers(propertyConfig.getConcurrentConsumerSize());
        container.setGlobalQos(true);
        container.setPrefetchCount(propertyConfig.getPrefetchCount());
        container.afterPropertiesSet();
        container.setTaskExecutor(threadPoolTaskExecutor);
        return container;
    }
}

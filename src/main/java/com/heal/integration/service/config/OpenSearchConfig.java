package com.heal.integration.service.config;

import com.appnomic.appsone.common.util.StringUtils;
import com.heal.configuration.pojos.OSIndexZoneDetails;
import com.heal.configuration.pojos.TenantOpenSearchDetails;
import com.heal.integration.service.repo.RedisRepo;
import com.heal.integration.service.repo.cache.CacheWrapper;
import com.heal.integration.service.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.auth.AuthScope;
import org.apache.hc.client5.http.auth.UsernamePasswordCredentials;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.auth.BasicCredentialsProvider;
import org.apache.hc.client5.http.impl.nio.PoolingAsyncClientConnectionManager;
import org.apache.hc.client5.http.impl.nio.PoolingAsyncClientConnectionManagerBuilder;
import org.apache.hc.core5.http.HttpHost;
import org.apache.hc.core5.reactor.IOReactorConfig;
import org.apache.hc.core5.util.TimeValue;
import org.opensearch.client.json.jackson.JacksonJsonpMapper;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.transport.OpenSearchTransport;
import org.opensearch.client.transport.httpclient5.ApacheHttpClient5TransportBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.opensearch.client.opensearch.nodes.info.NodeInfo;
import java.util.stream.Collectors;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class OpenSearchConfig {
    @Autowired
    HealthMetrics metrics;

    @Autowired
    RedisRepo redisRepo;

    @Autowired
    CacheWrapper cacheWrapper;

    @Value("${opensearch.connection.io.reactor.size:2}")
    int osIOReactor;

    @Value("${opensearch.connection.max.redirects:10}")
    int osMaxRedirects;

    private final List<OpenSearchClient> openSearchClients = new ArrayList<>();
    private final ConcurrentMap<String, OpenSearchClient> accountZoneOpenSearchConfigMap = new ConcurrentHashMap<>();

    public OpenSearchClient getOpenSearchClient(String accountIdentifier, String indexName) {
        String zone;
        List<OSIndexZoneDetails> osIndexZoneDetails = redisRepo.getHealIndexZones();
        if (osIndexZoneDetails == null || osIndexZoneDetails.isEmpty()) {
            log.warn("No OS Index to zone mappings found in redis. Defaulting to 'MISC' zone. Account:{}, index:{}", accountIdentifier, indexName);
            zone = "MISC";
        } else {
            OSIndexZoneDetails zoneDetails = osIndexZoneDetails.stream().filter(c -> c.getIndexName().equals(indexName)).findAny().orElse(null);
            if (zoneDetails == null) {
                log.warn("No zone found for index:{}, account:{}. Defaulting to 'MISC' zone.", indexName, accountIdentifier);
                zone = "MISC";
            } else {
                zone = zoneDetails.getZoneName();
            }
        }

        List<TenantOpenSearchDetails> tenantOpenSearchDetailsList = cacheWrapper.getTenantOpenSearchDetails(accountIdentifier);
        if (tenantOpenSearchDetailsList == null || tenantOpenSearchDetailsList.isEmpty()) {
            log.error("No Tenant-OpenSearch mapping list found for account:{}, index:{}", accountIdentifier, indexName);
            return null;
        }

        TenantOpenSearchDetails tenantOpenSearchDetails = tenantOpenSearchDetailsList.stream()
                .filter(c -> c.getZone().equals(zone)).findAny().orElse(null);
        if (tenantOpenSearchDetails == null) {
            log.error("No OpenSearch tenant configuration found for account:{}, zone: {}", accountIdentifier, zone);
            return null;
        }

        String key = accountIdentifier + "_" + zone;
        OpenSearchClient existingClient = accountZoneOpenSearchConfigMap.get(key);

        boolean needsReinitialization = false;
        if (existingClient == null) {
            needsReinitialization = true;
            log.warn("OpenSearch client is null for key: {}. Initializing.", key);
        } else {
            // 2. If a client exists, check if it's healthy.
            try {
                if (!existingClient.ping().value()) {
                    log.warn("Ping failed for OpenSearch client with key: {}. Re-initializing.", key);
                    needsReinitialization = true;
                } else {
                    // This is the healthy, reusable path.
                    log.debug("OpenSearch Client is healthy and initialized for account {} and zone {}", accountIdentifier, zone);
                }
            } catch (Exception e) {
                log.warn("Ping check threw an exception for client with key: {}. Re-initializing.", key, e);
                needsReinitialization = true;
            }
        }

        // 3. If we need a new client, create and cache it.
        if (needsReinitialization) {
            // Clean up the old client if it exists and failed the health check
            if (existingClient != null) {
                try {
                    existingClient.shutdown();
                } catch (Exception e) {
                    log.error("Exception while shutting down stale client for key: {}", key, e);
                }
            }
            existingClient = createOpenSearchClient(tenantOpenSearchDetails, accountIdentifier, indexName);
            if (existingClient != null) {
                accountZoneOpenSearchConfigMap.put(key, existingClient);
            }
        }

        return existingClient;
    }

    private OpenSearchClient createOpenSearchClient(TenantOpenSearchDetails openSearchDetails, String accountIdentifier, String indexName) {
        // Custom thread factory for naming threads for both IO reactor and connection pool
        ThreadFactory customThreadFactory = new OpenSearchClientThreadFactory(accountIdentifier + "-io-thread-");

        OpenSearchClient client;
        try {
            List<HttpHost> httpHosts = getHttpHostList(openSearchDetails);
            if (httpHosts.isEmpty()) {
                log.error("Http hosts list is empty. OpenSearch:{}, account:{}, index:{}", openSearchDetails, accountIdentifier, indexName);
                return null;
            }
            final OpenSearchTransport transport = ApacheHttpClient5TransportBuilder.builder(httpHosts.toArray(new HttpHost[0]))
                    .setMapper(new JacksonJsonpMapper())
                    .setHttpClientConfigCallback(httpClientBuilder -> {

                        // Create a default request configuration with timeouts
                        final RequestConfig requestConfig = RequestConfig.custom()
                                .setResponseTimeout(openSearchDetails.getSocketTimeout(), TimeUnit.MILLISECONDS)
                                .setConnectTimeout(openSearchDetails.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                                .setConnectionRequestTimeout(openSearchDetails.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                                .setConnectionKeepAlive(TimeValue.of(openSearchDetails.getKeepAliveSecs(), TimeUnit.SECONDS))
                                .setMaxRedirects(osMaxRedirects)
                                .build();

                        final PoolingAsyncClientConnectionManager connectionManager = PoolingAsyncClientConnectionManagerBuilder
                                .create()
                                .setMaxConnPerRoute(openSearchDetails.getPerRouteConnections())
                                .setMaxConnTotal(openSearchDetails.getMaxConnections())
                                .setDefaultConnectionConfig(ConnectionConfig.custom()
                                        .setConnectTimeout(openSearchDetails.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                                        .setSocketTimeout(openSearchDetails.getSocketTimeout(), TimeUnit.MILLISECONDS)
                                        .setTimeToLive(TimeValue.of(openSearchDetails.getKeepAliveSecs(), TimeUnit.SECONDS))
                                        .build())
                                .build();

                        if (!StringUtils.isEmpty(openSearchDetails.getUsername()) && !StringUtils.isEmpty(openSearchDetails.getEncryptedPassword())) {
                            final BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                            String decryptedPwd = new String(Base64.getDecoder().decode(openSearchDetails.getEncryptedPassword()), StandardCharsets.UTF_8);
                            httpHosts.forEach(httpHost ->
                                    credentialsProvider.setCredentials(new AuthScope(httpHost), new UsernamePasswordCredentials(openSearchDetails.getUsername(), decryptedPwd.toCharArray())));

                            return httpClientBuilder
                                    .setDefaultRequestConfig(requestConfig)
                                    .setDefaultCredentialsProvider(credentialsProvider)
                                    .setConnectionManager(connectionManager)
                                    .setThreadFactory(customThreadFactory)
                                    .setIOReactorConfig(IOReactorConfig.custom().setIoThreadCount(osIOReactor).build())
                                    .setDefaultRequestConfig(RequestConfig.custom()
                                            .setConnectionRequestTimeout(openSearchDetails.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                                            .setResponseTimeout(openSearchDetails.getSocketTimeout(), TimeUnit.MILLISECONDS)
                                            .setDefaultKeepAlive(openSearchDetails.getKeepAliveSecs(), TimeUnit.SECONDS)
                                            .setConnectionKeepAlive(TimeValue.of(openSearchDetails.getKeepAliveSecs(), TimeUnit.SECONDS))
                                            .setMaxRedirects(osMaxRedirects)
                                            .build())
                                    .setKeepAliveStrategy((r, c) -> TimeValue.ofSeconds(openSearchDetails.getKeepAliveSecs()));
                        } else {
                            return httpClientBuilder
                                    .setDefaultRequestConfig(requestConfig)
                                    .setConnectionManager(connectionManager)
                                    .setThreadFactory(customThreadFactory)
                                    .setIOReactorConfig(IOReactorConfig.custom().setIoThreadCount(osIOReactor).build())
                                    .setKeepAliveStrategy((r, c) -> TimeValue.ofSeconds(openSearchDetails.getKeepAliveSecs()));
                        }
                    }).build();

            client = new OpenSearchClient(transport);
            if (client.ping().value()) {
                log.info("Established connection with OpenSearch nodes [{}] with protocol:{}, username:{}, SocketTimeout: {}ms, ConnectTimeout: {}ms," +
                                " ConnectionRequestTimeout: {}ms, KeepAlive: {}s, MaxConnections: {}, MaxPerRouteConnections: {}, account:{}, index:{} ",
                        openSearchDetails.getNodeAddresses(), openSearchDetails.getProtocol(), openSearchDetails.getUsername(),
                        openSearchDetails.getSocketTimeout(), openSearchDetails.getConnectionTimeout(), openSearchDetails.getConnectionTimeout(),
                        openSearchDetails.getKeepAliveSecs(), openSearchDetails.getMaxConnections(), openSearchDetails.getPerRouteConnections(),
                        accountIdentifier, indexName);
            } else {
                throw new Exception(String.format("Unable to establish connection to OpenSearch nodes [%s]", openSearchDetails.getNodeAddresses()));
            }
        } catch (Throwable e) {
            metrics.updateOpenSearchErrors(1L);
            client = null;
            log.error("Error while establishing connection to OpenSearch nodes [{}], Protocol:{}, username:{}, account:{}, index:{}",
                    openSearchDetails.getNodeAddresses(), openSearchDetails.getProtocol(), openSearchDetails.getUsername(), accountIdentifier, indexName, e);
        }

        openSearchClients.add(client);
        return client;
    }

    private static List<HttpHost> getHttpHostList(TenantOpenSearchDetails openSearchDetails) {
        List<String> nodesList = Arrays.asList(openSearchDetails.getNodeAddresses().split(","));
        List<HttpHost> httpHosts = new ArrayList<>();
        nodesList.forEach(node -> {
            try {
                String[] hostAndPort = node.split(":");
                HttpHost httpHost = new HttpHost(openSearchDetails.getProtocol(), hostAndPort[0], Integer.parseInt(hostAndPort[1]));
                httpHosts.add(httpHost);
            } catch (Exception e) {
                log.error("Invalid OpenSearch host and port details, Host: {}", node, e);
            }
        });

        return httpHosts;
    }

    @PreDestroy
    public void shutdown() {
        log.info("Opensearch connection shutdown method called.");
        openSearchClients.stream().filter(Objects::nonNull).forEach(c -> {
            try {
                c.shutdown();
                log.info("Opensearch connection for node: {} shutdown successfully.", c.nodes().info().nodes().values().stream().map(NodeInfo::host).collect(Collectors.toSet()));
            } catch (Exception e) {
                log.error("Exception while closing OpenSearch client", e);
            }
        });
    }
}
package com.heal.integration.service.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.heal.integration.service.utility.CommonUtils;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.protocol.ProtocolVersion;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.resource.DefaultEventLoopGroupProvider;
import io.netty.util.concurrent.DefaultEventExecutorGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

@Configuration
@Slf4j
public class RedisConfig {
    private final CommonUtils commonUtils;

    public RedisConfig(CommonUtils commonUtils) {
        this.commonUtils = commonUtils;
    }

    @Bean
    @ConditionalOnProperty(name = "spring.redis.cluster.mode", havingValue = "false")
    RedisConnectionFactory standaloneRedisConnectionFactory(LettucePoolingClientConfiguration clientConfig,
                                                            PropertyConfig propertyConfig) {
        // create and return a RedisConnectionFactory for standalone mode
        log.debug("redis standalone connection factory initialization is in process...");
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();

        if (propertyConfig.getRedisUsername() != null && !propertyConfig.getRedisUsername().trim().isEmpty()) {
            redisStandaloneConfiguration.setUsername(propertyConfig.getRedisUsername());
        }

        if (propertyConfig.getRedisPassword() != null && !propertyConfig.getRedisPassword().trim().isEmpty()) {
            redisStandaloneConfiguration.setPassword(commonUtils.getDecryptedData(propertyConfig.getRedisPassword().trim()));
        }

        String[] hostAndPort = propertyConfig.getClusterNodes().get(0).split(":");
        redisStandaloneConfiguration.setHostName(hostAndPort[0]);
        redisStandaloneConfiguration.setPort(Integer.parseInt(hostAndPort[1]));

        LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(redisStandaloneConfiguration, clientConfig);
        lettuceConnectionFactory.setShareNativeConnection(propertyConfig.isShareNativeConnection());
        lettuceConnectionFactory.setValidateConnection(false);
        lettuceConnectionFactory.afterPropertiesSet();

        log.info("redis standalone connection factory initialization completed with ssl {} ",
                lettuceConnectionFactory.isUseSsl());
        return lettuceConnectionFactory;
    }

    @Bean
    @ConditionalOnProperty(name = "spring.redis.cluster.mode", havingValue = "true")
    RedisConnectionFactory clusterRedisConnectionFactory(LettucePoolingClientConfiguration clientConfig,
                                                         PropertyConfig propertyConfig) {
        // create and return a RedisConnectionFactory for cluster mode
        log.debug("redis cluster connection factory initialization is in process...");
        RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration(propertyConfig.getClusterNodes());

        if (propertyConfig.getRedisUsername() != null && !propertyConfig.getRedisUsername().trim().isEmpty()) {
            redisClusterConfiguration.setUsername(propertyConfig.getRedisUsername());
        }

        if (propertyConfig.getRedisPassword() != null && !propertyConfig.getRedisPassword().trim().isEmpty()) {
            redisClusterConfiguration.setPassword(commonUtils.getDecryptedData(propertyConfig.getRedisPassword().trim()));
        }

        LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(redisClusterConfiguration, clientConfig);
        lettuceConnectionFactory.setShareNativeConnection(propertyConfig.isShareNativeConnection());
        lettuceConnectionFactory.setValidateConnection(false);
        lettuceConnectionFactory.afterPropertiesSet();

        log.info("redis cluster connection factory initialization completed with ssl {}",
                lettuceConnectionFactory.isUseSsl());
        return lettuceConnectionFactory;
    }

    @Bean
    @ConditionalOnProperty(name = "spring.redis.cluster.mode", havingValue = "false")
    ClientOptions clientStandaloneOptions() {
        log.debug("redis standalone client options initialization in process");
        return ClusterClientOptions.builder()
                .autoReconnect(true)
                .pingBeforeActivateConnection(true)
                .build();
    }

    @Bean
    @ConditionalOnProperty(name = "spring.redis.cluster.mode", havingValue = "true")
    ClientOptions clientClusterOptions() {
        log.debug("redis cluster client options initialization in process");
        return ClusterClientOptions.builder()
                .autoReconnect(true)
                .protocolVersion(ProtocolVersion.RESP3)
                .validateClusterNodeMembership(true)
                .maxRedirects(5)
                .cancelCommandsOnReconnectFailure(true)
                .pingBeforeActivateConnection(true)
                .topologyRefreshOptions(ClusterTopologyRefreshOptions.builder()
                        .enablePeriodicRefresh()
                        .build())
                .build();
    }

    @Bean
    LettucePoolingClientConfiguration clientConfig(ClientOptions clientOptions, PropertyConfig propertyConfig) {
        log.debug("Lettuce pool config method called.");

        GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMaxIdle(propertyConfig.getMaxIdleConnections());
        poolConfig.setMinIdle(propertyConfig.getMinIdleConnections());
        poolConfig.setMaxWait(Duration.ofSeconds(propertyConfig.getMaxWaitTimeInSecs()));
        poolConfig.setMaxTotal(propertyConfig.getMaxTotalConnections());


        ClientResources clientResources = DefaultClientResources.builder()
                .eventExecutorGroup(new DefaultEventExecutorGroup(5, new ThreadFactoryBuilder().setNameFormat("lettuce-event-group-%d").build()))
                .eventLoopGroupProvider(new DefaultEventLoopGroupProvider(6))
                .ioThreadPoolSize(7)
                .build();

        if (propertyConfig.isRedisSSLEnabled()) {
            return LettucePoolingClientConfiguration.builder()
                    .poolConfig(poolConfig)
                    .clientOptions(clientOptions)
                    .commandTimeout(Duration.ofSeconds(propertyConfig.getMaxWaitTimeInSecs()))
                    .clientResources(clientResources)
                    .useSsl()
                    .build();
        } else {
            return LettucePoolingClientConfiguration.builder()
                    .clientOptions(clientOptions)
                    .poolConfig(poolConfig)
                    .clientResources(clientResources)
                    .commandTimeout(Duration.ofSeconds(propertyConfig.getMaxWaitTimeInSecs()))
                    .build();
        }
    }

    @Bean
    RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        log.debug("redis template initialization started...");
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        template.setEnableTransactionSupport(true);
        template.afterPropertiesSet();
        log.debug("redis template initialization completed");

        return template;
    }

}

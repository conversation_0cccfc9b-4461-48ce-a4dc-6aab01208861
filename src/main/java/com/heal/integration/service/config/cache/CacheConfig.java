package com.heal.integration.service.config.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {
    @Value("${redis.cache.mode:0}")
    private int redisCacheMode;
    @Value("${topology.configuration.cache.max.size:10000}")
    private int topologyCacheMaxsize;
    @Value("${topology.configuration.cache.expire.interval.minutes:20}")
    private int topologyCacheExpirationTime;
    @Value("${masterData.configuration.cache.max.size:1000}")
    private int masterDataCacheMaxsize;
    @Value("${masterData.configuration.cache.expire.interval.minutes:60}")
    private int masterDataCacheExpirationTime;
    @Value("${account.configuration.cache.max.size:10000}")
    private int accountCacheMaxsize;
    @Value("${account.configuration.cache.expire.interval.minutes:20}")
    private int accountCacheExpirationTime;
    @Value("${incident.profile.configuration.cache.max.size:10000}")
    private int incidentProfileCacheMaxsize;
    @Value("${incident.profile.configuration.cache.expire.interval.minutes:20}")
    private int incidentProfileCacheExpirationTime;
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        if (redisCacheMode == 0) {
            cacheManager.registerCustomCache(CacheConstants.TOPOLOGY_CACHE, createCacheConfig(topologyCacheMaxsize, topologyCacheExpirationTime));
            cacheManager.registerCustomCache(CacheConstants.MASTER_DATA_CACHE, createCacheConfig(masterDataCacheMaxsize, masterDataCacheExpirationTime));
            cacheManager.registerCustomCache(CacheConstants.ACCOUNT_CACHE, createCacheConfig(accountCacheMaxsize, accountCacheExpirationTime));
            cacheManager.registerCustomCache(CacheConstants.INCIDENT_PROFILE_CACHE, createCacheConfig(accountCacheMaxsize, accountCacheExpirationTime));
        }
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }

    private Cache<Object, Object> createCacheConfig(int maxSize, int expireTime) {
        Cache<Object, Object> cache;
        cache = Caffeine.newBuilder()
                .initialCapacity(maxSize / 10)
                .maximumSize(maxSize)
                .expireAfterWrite(expireTime, TimeUnit.MINUTES)
                .recordStats()
                .build();

        return cache;
    }

}

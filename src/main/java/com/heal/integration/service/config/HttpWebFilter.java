package com.heal.integration.service.config;

import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

import java.io.IOException;
import java.util.List;

@WebFilter("*")
@Slf4j
public class HttpWebFilter implements Filter {

    @Value("#{'${http.blocked.methods:TRACE,TRACK}'.split(',')}")
    private List<String> httpMethodsNotAllowedList;

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) res;

        if (httpMethodsNotAllowedList.stream().anyMatch(h -> h.equalsIgnoreCase(request.getMethod()))) {
            response.setStatus(HttpStatus.METHOD_NOT_ALLOWED.value());
            response.setContentType(MediaType.TEXT_PLAIN_VALUE);
            response.sendError(HttpStatus.METHOD_NOT_ALLOWED.value());
            log.error("Method not allowed: [{}], for path: [{}]", request.getMethod(), request.getServletPath());
            return;
        }

        chain.doFilter(req, res);
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void destroy() {
    }
}


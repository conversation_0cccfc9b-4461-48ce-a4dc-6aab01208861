package com.heal.integration.service.config;

import lombok.Data;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR> <PERSON>man - 04-04-2024
 */

@Configuration
@Data
public class PropertyConfig {

    // http config
    @Value("${http.connect.timeout.sec:5}")
    private int connectionTimeout;
    @Value("${http.read.timeout.sec:10}")
    private int readTimeout;

    // redis config
    @Value("${spring.redis.cluster.nodes:localhost:6379}")
    private List<String> clusterNodes;
    @Getter
    @Value("${spring.redis.username:}")
    private String redisUsername;
    @Getter
    @Value("${spring.redis.password:}")
    private String redisPassword;
    @Value("${spring.redis.cluster.mode:true}")
    private boolean redisClusterMode;
    @Value("${spring.redis.share.native.connection:false}")
    private boolean shareNativeConnection;
    @Value("${spring.redis.max.wait.secs:5}")
    private int maxWaitTimeInSecs;
    @Value("${spring.redis.max.idle.connections:500}")
    private int maxIdleConnections;
    @Value("${spring.redis.min.idle.connections:500}")
    private int minIdleConnections;
    @Value("${spring.redis.max.total.connections:500}")
    private int maxTotalConnections;
    @Value("${spring.redis.ssl:true}")
    private boolean redisSSLEnabled;
    @Value("${redis.key.expire.min:15}")
    private long keyExpireInMin;

    // opensearch config
    @Value("${opensearch.batch.size:50}")
    private int osBatchSize;
    @Value("${opensearch.batch.queue.max.size:10000}")
    private int maxQueueSize;
    @Value("${opensearch.connection.io.reactor.size:5}")
    public int osReactorSize;

    // keycloak config
    @Value("${keycloak.ip:localhost}")
    public String keycloakIp;
    @Value("${keycloak.port:9443}")
    public String keycloakPort;
    @Getter
    @Value("${keycloak.user:appsoneadmin}")
    public String keycloakUsername;
    @Getter
    @Value("${keycloak.pwd:QXBwc29uZUAxMjM=}")
    public String keycloakEncryptedPassword;

    // llm module-aiops related config
    @Value("${llm.url:http://localhost:1323/v1/chat/completions}")
    private String llmServiceUrl;
    // this time is for suppressing incident criteria
    @Value("${llm.criteria.suppress.per.incident.min:5}")
    private long suppressIncidentCriteria;
    // this time is for suppressing llm api call
    @Value("${llm.suppress.api.per.min:2}")
    private long suppressApiCall;

    // other config
    @Value("${minimum.service.count:2}")
    private int minimumServiceCount;
    @Value("${maximum.questionandanswer.os:3}")
    private int maximumQuestionAndAnswerOS;

    // rabbitmq threadpool configs
    @Value("${thread.pool.queue.capacity:1}")
    private int queueCapacity;
    @Value("${spring.rabbitmq.signalMessageAIOps.concurrentConsumerSize:1}")
    private int concurrentConsumerSize;
    @Value("${spring.rabbitmq.addresses:rabbitmq.appnomic:5672}")
    private String addresses;
    @Getter
    @Value("${spring.rabbitmq.username:guest}")
    private String username;
    @Getter
    @Value("${spring.rabbitmq.password:Z3Vlc3Q=}")
    private String password;
    @Value("${spring.rabbitmq.ssl.enabled:true}")
    private boolean sslEnabled;
    @Value("${spring.rabbitmq.ssl.algorithm:TLSv1.3}")
    private String algorithm;
    @Value("${spring.rabbitmq.signalMessageAIOpsQueueName:signal-messages-aiops}")
    public String signalMessageAIOpsQueueName;
    @Value("${spring.rabbitmq.signalMessageAIOps.prefetchCount:10}")
    private int prefetchCount;
    @Value("${spring.rabbitmq.signalMessageAIOps.acknowledgementMode:AUTO}")
    private String acknowledgementMode;
    @Value("${prompt.message.system.role:}")
    private String promptMessageSystemRole;
}

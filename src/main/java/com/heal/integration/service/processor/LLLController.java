package com.heal.integration.service.processor;

import com.heal.configuration.pojos.LlmQuestion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * <AUTHOR> - 09-05-2024
 */
@Slf4j
@RestController
@RequestMapping("/api/v1.0")
public class LLLController {

    private final QAProcessor qaProcessor;

    public LLLController(QAProcessor qaProcessor) {
        this.qaProcessor = qaProcessor;
    }

    @PostMapping(value = "/conversation", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> getQuestion(@Valid @RequestBody LlmQuestion llmQuestion,
                                    @RequestHeader(value = "Authorization", required = true) String authorization) {
        log.info("Request received from conversation api {}", llmQuestion);
        return qaProcessor.processValidateQARequest(llmQuestion, authorization);
    }
}

package com.heal.integration.service.processor;

import com.heal.integration.service.pojo.profiling.AccountProfile;
import com.heal.integration.service.pojo.profiling.IncidentProfile;
import com.heal.integration.service.repo.OpenSearchRepo;
import com.heal.integration.service.repo.RedisRepo;
import com.heal.integration.service.utility.Constants;
import com.heal.integration.service.utility.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
@Service
public class RedisKeysDeleteScheduler {
    @Value("${fetch.os.closed.signals.interval.days:30}")
    private int closedSignalInterval;

    private final RedisUtilities redisUtilities;
    private final OpenSearchRepo openSearchRepo;
    private final RedisRepo redisRepo;
    private static final int CHUNK_SIZE = 30;


    public RedisKeysDeleteScheduler(RedisUtilities redisUtilities, OpenSearchRepo openSearchRepo, RedisRepo redisRepo) {
        this.redisUtilities = redisUtilities;
        this.openSearchRepo = openSearchRepo;
        this.redisRepo = redisRepo;
    }

    @Scheduled(initialDelayString = "${redis.delete.closed.signal.scheduler.initial.delay.seconds:300}", fixedRateString =
            "${redis.delete.closed.signal.scheduler.interval.seconds:3600}", timeUnit = TimeUnit.SECONDS)
    public void deleteClosedSignalCriteriaFromRedis() {
        log.trace("'deleteClosedSignalCriteriaFromRedis' scheduler method called.");
        // TODO : this is a blocking call because taking keys based on pattern using keys api is blocking operation
        Set<String> keys = redisUtilities.getKeysByPattern(Constants.AIOPS_CRITERIA_REDIS_KEY_PATTERN);
        if (keys.isEmpty()) {
            log.debug("Redis keys not found with pattern: [{}]", Constants.AIOPS_CRITERIA_REDIS_KEY_PATTERN);
            return;
        }

        Set<String> incidentIds = getSignalsFromRedisKeys(keys);
        log.debug("Total signals found in redis are {}", incidentIds.size());

        long currentTimeMillis = System.currentTimeMillis();
        long fromTime = Instant.ofEpochMilli(currentTimeMillis).minus(Duration.ofDays(closedSignalInterval)).toEpochMilli();

        Map<String, Set<String>> accountSignalMap = new HashMap<>();

        for (String signalid : incidentIds) {
            IncidentProfile incidentProfile = redisRepo.getIncidentCriteria(signalid);
            for (AccountProfile accountProfile : incidentProfile.getAccounts()) {
                if (accountSignalMap.containsKey(accountProfile.getAccountId())) {
                    accountSignalMap.get(accountProfile.getAccountId()).add(signalid);
                } else {
                    Set<String> signals = new HashSet<>();
                    signals.add(signalid);
                    accountSignalMap.put(accountProfile.getAccountId(), signals);
                }
            }
        }
        log.debug("account to signal mapping in aiops key is {}", accountSignalMap);
        for (String accIdentifier : accountSignalMap.keySet()) {
            List<String> signalsList = new ArrayList<>(accountSignalMap.get(accIdentifier)); // Convert Set to List for easy chunking
            int totalIncidents = signalsList.size();
            log.debug("Total signals to be deleted are {} for accountid {}", totalIncidents, accIdentifier);
            for (int i = 0; i < totalIncidents; i += CHUNK_SIZE) {
                List<String> signalChunk = signalsList.subList(i, Math.min(totalIncidents, i + CHUNK_SIZE));
                log.trace("signal to be deleted as chunk is {}", signalChunk);

                long osCurrentTimeMillis = System.currentTimeMillis();
                Set<String> signalsWithClosedStatus = openSearchRepo.getSignalsWithClosedStatus(fromTime,
                        currentTimeMillis, new HashSet<>(signalChunk), accIdentifier, Constants.SIGNAL_CLOSED_STATUS);
                log.debug("Total time taken to fetch closed signal from OS {} ms", System.currentTimeMillis() - osCurrentTimeMillis);

                log.debug("Total incident with status: [{}] is {} in {} day interval for accountId {}",
                        Constants.SIGNAL_CLOSED_STATUS, signalsWithClosedStatus, closedSignalInterval, accIdentifier);

                List<String> otherSignals = signalChunk
                        .stream()
                        .filter(incident -> signalsWithClosedStatus
                                .stream()
                                .noneMatch(closedIncident -> closedIncident.contains(incident)))
                        .collect(Collectors.toList());

                osCurrentTimeMillis = System.currentTimeMillis();
                Set<String> signalsWithUpgradedStatus = openSearchRepo.getSignalsWithClosedStatus(fromTime,
                        currentTimeMillis, new HashSet<>(otherSignals), accIdentifier, Constants.SIGNAL_UPGRADED_STATUS);
                log.debug("Total time taken to fetch upgraded signal from OS {} ms", System.currentTimeMillis() - osCurrentTimeMillis);

                log.debug("Total incident with status: [{}] is {} in {} day interval for accountId {}",
                        Constants.SIGNAL_UPGRADED_STATUS, signalsWithUpgradedStatus, closedSignalInterval, accIdentifier);

                signalsWithClosedStatus.addAll(signalsWithUpgradedStatus);
                if (!signalsWithClosedStatus.isEmpty()) {
                    log.debug("Total keys to be deleted is {}", signalsWithClosedStatus);
                    redisUtilities.deleteMultipleKeys(signalsWithClosedStatus);
                }
            }
        }
    }

    public Set<String> getSignalsFromRedisKeys(Set<String> keys) {
        Pattern regex = Pattern.compile(Constants.SIGNAL_ID_REGEX_AIOPS_CRITERIA);
        return keys.stream()
                .map(regex::matcher)
                .filter(Matcher::matches)
                .map(matcher -> matcher.group(1))
                .collect(Collectors.toSet());
    }
}

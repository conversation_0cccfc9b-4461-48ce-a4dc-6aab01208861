package com.heal.integration.service.processor;

import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.Account;
import com.heal.integration.service.config.PropertyConfig;
import com.heal.integration.service.pojo.IncidentDetails;
import com.heal.integration.service.pojo.LLMRequestPojo;
import com.heal.integration.service.pojo.profiling.*;
import com.heal.integration.service.pojo.topology.Topology;
import com.heal.integration.service.repo.RedisRepo;
import com.heal.integration.service.repo.cache.IncidentProfileCacheService;
import com.heal.integration.service.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProcessEventDetails {

    private final RedisRepo redisRepo;
    private final LLMService llmService;
    private final HealthMetrics healthMetrics;
    private final IncidentProfileCacheService incidentProfileCacheService;
    private final PropertyConfig propertyConfig;
    private final LLmRestApiPusher llmRestApiPusher;
    @Autowired
    private ObjectMapper objectMapper;

    public ProcessEventDetails(RedisRepo redisRepo, LLMService llmService,
                               HealthMetrics healthMetrics, IncidentProfileCacheService incidentProfileCacheService,
                               PropertyConfig propertyConfig, LLmRestApiPusher llmRestApiPusher) {
        this.redisRepo = redisRepo;
        this.llmService = llmService;
        this.healthMetrics = healthMetrics;
        this.incidentProfileCacheService = incidentProfileCacheService;
        this.propertyConfig = propertyConfig;
        this.llmRestApiPusher = llmRestApiPusher;
    }

    public void processIncidentDetailsForRCA(SignalProtos.SignalDetails signalDetailsProto) {
        log.info("Processing event details.");
        long startTime = System.currentTimeMillis();
        try {
            List<SignalProtos.AnomalyDetail> anomalyList = signalDetailsProto.getAnomalyDetailsList();
            if (anomalyList.isEmpty()) {
                log.warn("No anomalies found.");
                return;
            }

            Account account = getAccount(signalDetailsProto.getAccountId());
            if (account == null) {
                //TODO: Add the error log and return
                log.error("Not a valid accountId {}, incidentId {}", signalDetailsProto.getAccountId(), signalDetailsProto.getSignalId());
                return;
            }

            Set<String> services = new HashSet<>(signalDetailsProto.getRootCauseServiceIdsList());
            if (!signalDetailsProto.getEntryServiceId().isEmpty()) {
                services.add(signalDetailsProto.getEntryServiceId());
            }
            services.addAll(signalDetailsProto.getServiceIdsList());
            log.debug("Total services are {}", services.size());

            IncidentProfile newProfile = getCurrentIncidentProfile(signalDetailsProto, services);
            IncidentProfile redisProfile = redisRepo.getIncidentCriteria(newProfile.getIncidentId());
            boolean isIncidentProfileCached = incidentProfileCacheService.isIncidentProfileCached(redisProfile,
                    newProfile, signalDetailsProto.getAccountId());

            // if isIncidentProfileCached is true, then we need to update the local cache with the new profile
            // and update the redis cache with the new profile

            // if isIncidentProfileCached is false, then we need to we have to add new profile in local cache and
            // redis cache
            if (services.size() >= propertyConfig.getMinimumServiceCount()) {
                if (isIncidentProfileCached) {
                    log.info("incident profile cached for incidentId {}", newProfile.getIncidentId());
                    processCachedProfile(signalDetailsProto, newProfile, services);
                } else {
                    log.info("minimum service count condition matched");
                    boolean isRequestPushedToQueue = criteriaMatchExecution(services, signalDetailsProto,
                            "IncidentProfile is not cached. So, criteria met");
                    if (isRequestPushedToQueue) {
                        // update the local cache with the new profile
                        incidentProfileCacheService.updateIncidentProfile(newProfile);
                        // update the redis cache with the new profile
                        redisRepo.saveIncidentCriteria(newProfile.getIncidentId(), newProfile);
                        log.info("llm request is pushed to queue so cache updating is done");
                    }
                }
            }

        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Unable to process event details.", e);
        } finally {
            long endTime = System.currentTimeMillis() - startTime;
            log.info("Total time taken to process event data - {}ms", endTime);
        }
    }

    private void processCachedProfile(SignalProtos.SignalDetails signalDetailsProto, IncidentProfile newProfile, Set<String> services) {
        try {
            IncidentProfile cacheProfile = incidentProfileCacheService.getLastCacheIncidentProfile(newProfile);

            boolean matchedCriteria = false;
            StringBuilder criteriaDetails = new StringBuilder();
            Map<String, AccountProfile> accountCacheMap;

            if (cacheProfile != null) {
                log.trace("local cache profile available {}", cacheProfile);

                IncidentProfile copiedProfile = objectMapper.readValue(objectMapper.writeValueAsString(cacheProfile),
                        IncidentProfile.class);

                accountCacheMap = copiedProfile.getAccounts()
                        .parallelStream()
                        .collect(Collectors.toMap(AccountProfile::getAccountId, Function.identity()));

                matchedCriteria |= incidentProfileCacheService.updateServices(accountCacheMap, newProfile, criteriaDetails);
                matchedCriteria &= incidentProfileCacheService.isTimeDifferenceGreaterThanNMinutes(newProfile, copiedProfile);

                if (matchedCriteria) {
                    log.debug("criteria details found is {}", criteriaDetails);
                    boolean isRequestPushedToQueue = criteriaMatchExecution(services, signalDetailsProto,
                            criteriaDetails.toString());
                    if (isRequestPushedToQueue) {
                        redisRepo.saveIncidentCriteria(copiedProfile.getIncidentId(), copiedProfile);
                        incidentProfileCacheService.updateIncidentProfile(copiedProfile);
                        log.info("llm request is pushed to queue so redis/local cache updating is done");
                    }
                } else {
                    log.debug("No criteria matched for incident {}", copiedProfile.getIncidentId());
                }
            }
        } catch (Exception e) {
            log.error("Unable to process cached profile.", e);
        }
    }

    private Account getAccount(String accountId) {
        List<Account> accountList = redisRepo.getAccounts();
        return accountList.stream()
                .filter(acc -> acc.getIdentifier().equals(accountId))
                .findFirst()
                .orElse(null);
    }

    public IncidentProfile getCurrentIncidentProfile(SignalProtos.SignalDetails signalDetailsProto, Set<String> services) {
        try {
            Set<String> kpiIdSet = signalDetailsProto.getAnomalyDetailsList().stream().map(anomaly -> {
                        try {
                            return anomaly.getKpiId();
                        } catch (NumberFormatException nfe) {
                            log.error("error while parsing kpi id {}. ", anomaly.getKpiId(), nfe);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            log.trace("unique kpi ids are {}", kpiIdSet);

            Set<String> categories = signalDetailsProto.getAnomalyDetailsList().stream()
                    .map(SignalProtos.AnomalyDetail::getCategoryId)
                    .collect(Collectors.toSet());
            log.trace("categories from anomalies are {}", categories.size());

            ArrayList<AccountProfile> accounts = new ArrayList<>();

            accounts.add(AccountProfile
                    .builder()
                    .accountId(signalDetailsProto.getAccountId())
                    .kpis(Kpis.builder().count(kpiIdSet.size()).names(kpiIdSet).build())
                    .services(Services.builder().count(services.size()).names(services).build())
                    .categories(Categories.builder().count(categories.size()).names(categories).build())
                    .severity(signalDetailsProto.getSignalSeverityId())
                    .build());

            return IncidentProfile.builder()
                    .incidentId(signalDetailsProto.getSignalId())
                    .lastTimeSignalReceived(System.currentTimeMillis())
                    .accounts(accounts)
                    .build();
        } catch (Exception e) {
            log.error("Unable to get current incident profile.", e);
            throw e;
        }
    }

    public boolean criteriaMatchExecution(Set<String> services, SignalProtos.SignalDetails signalDetailsProto,
                                          String criteriaDetails) {
        try {
            log.info("Matched criteria. accountId {}, signalId {}.", signalDetailsProto.getAccountId(), signalDetailsProto.getSignalId());

            Topology topology = new Topology();
            services.forEach(x -> llmService.getOutboundTopologyByService(signalDetailsProto.getAccountId(), x, topology));

            topology.generateTopology();
            log.trace("Connections topology {}", String.join(", ", topology.getPaths()));

            boolean isLLMCallAllowed = isLLMCallAllowed();

            if (isLLMCallAllowed) {
                log.info("Api suppress time is less than elapsed time, So making llm api call");

                LLMRequestPojo healLLMSignalPojo = llmService.getLLmRequestForRCA(signalDetailsProto, topology.getPaths());
                llmRestApiPusher.addToQueue(IncidentDetails
                        .builder()
                        .llmRequestPojo(healLLMSignalPojo)
                        .incidentId(signalDetailsProto.getSignalId())
                        .criteriaDetails(criteriaDetails)
                        .accountId(signalDetailsProto.getAccountId())
                        .build());

                incidentProfileCacheService.evictCache("llm");
                incidentProfileCacheService.getLastLLMCallTime("llm");

                return true;
            }
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Unable to process event details.", e);
        }
        return false;
    }

    private boolean isLLMCallAllowed() {
        long llmLastCallTime = incidentProfileCacheService.getLastLLMCallTime("llm");
        long current = System.currentTimeMillis();
        boolean isLLMCallAllowed = (current - llmLastCallTime) >= (propertyConfig.getSuppressApiCall() * 60 * 1000);

        log.debug("LLM call criteria match {} with cache value {}, current value {}", isLLMCallAllowed,
                llmLastCallTime, current);
        return isLLMCallAllowed;
    }
}

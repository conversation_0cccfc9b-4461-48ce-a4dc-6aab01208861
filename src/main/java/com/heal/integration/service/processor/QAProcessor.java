package com.heal.integration.service.processor;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.LlmAnswer;
import com.heal.configuration.pojos.LlmQuestion;
import com.heal.configuration.pojos.User;
import com.heal.configuration.pojos.opensearch.LlmSignalQAData;
import com.heal.integration.service.config.PropertyConfig;
import com.heal.integration.service.exceptions.AuthException;
import com.heal.integration.service.exceptions.HandlerException;
import com.heal.integration.service.exceptions.InvalidRequestBodyException;
import com.heal.integration.service.exceptions.LLMServiceException;
import com.heal.integration.service.pojo.LlmSignalRCAData;
import com.heal.integration.service.pojo.api.CustomResponse;
import com.heal.integration.service.repo.OpenSearchRepo;
import com.heal.integration.service.repo.RedisRepo;
import com.heal.integration.service.utility.Constants;
import com.heal.integration.service.utility.HealthMetrics;
import com.heal.integration.service.utility.KeyCloakAuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR> Suman - 09-05-2024
 */
@Service
@Slf4j
public class QAProcessor {
    private final KeyCloakAuthUtils keyCloakAuthUtils;
    private final HealthMetrics healthMetrics;
    private final LLMService llmService;
    private final OpenSearchRepo opensearchRepo;
    private final RedisRepo redisRepo;
    private final PropertyConfig propertyConfig;

    public QAProcessor(KeyCloakAuthUtils keyCloakAuthUtils, HealthMetrics healthMetrics, LLMService llmService,
                       OpenSearchRepo opensearchRepo, RedisRepo redisRepo, PropertyConfig propertyConfig) {
        this.keyCloakAuthUtils = keyCloakAuthUtils;
        this.healthMetrics = healthMetrics;
        this.llmService = llmService;
        this.opensearchRepo = opensearchRepo;
        this.redisRepo = redisRepo;
        this.propertyConfig = propertyConfig;
    }

    public ResponseEntity<?> processValidateQARequest(LlmQuestion llmQuestion, String authorization) {
        try {
            boolean isValid = keyCloakAuthUtils.isValidKey(authorization);

            if (!isValid) {
                healthMetrics.updateUnauthorizedRequests();
                log.error("Authorization token is invalid");
                throw new AuthException("Authorization token is invalid", HttpStatus.UNAUTHORIZED.value());
            }

            String userId = keyCloakAuthUtils.getUserIdentifier(authorization);
            if (!userId.equals(llmQuestion.getUserId())) {
                healthMetrics.updateUnauthorizedRequests();
                log.error("Not a valid user {}", llmQuestion.getUserId());
                throw new AuthException("Not a valid user", HttpStatus.UNAUTHORIZED.value());
            }

            User user = redisRepo.getUser(userId);
            if (user == null) {
                log.error("User details for {} not found in redis.", userId);
                throw new AuthException(String.format("User details for [%s] not found.", userId),
                        HttpStatus.UNAUTHORIZED.value());
            }

            Account account = redisRepo.getAccounts().stream()
                    .filter(acc -> acc.getIdentifier().equals(llmQuestion.getAccountIdentifier()))
                    .findFirst().orElse(null);

            if (account == null) {
                log.error("Invalid account id provided {}", llmQuestion.getAccountIdentifier());
                throw new InvalidRequestBodyException("Not a valid account id " + llmQuestion.getAccountIdentifier());
            }

            Map<String, String> contextFormation = new LinkedHashMap<>();
            LlmSignalRCAData llmSignalRCAData = opensearchRepo.getLatestRCABySignal(llmQuestion);

            if (llmSignalRCAData == null) {
                log.error("No RCA found for the signal {}", llmQuestion.getIncidentIdentifier());
                throw new InvalidRequestBodyException("No RCA found for the signal " + llmQuestion.getIncidentIdentifier());
            }

            contextFormation.put(Constants.ROLE_SYSTEM, llmSignalRCAData.getRequestData());

            if (propertyConfig.getMaximumQuestionAndAnswerOS() > 0) {
                opensearchRepo.getLatestTopNQandAByUUID(llmQuestion, contextFormation);
            }

            LlmSignalQAData llmSignalQAData = llmService.llmQAServiceCall(llmQuestion, contextFormation);

            opensearchRepo.addQAResponseToOS(llmSignalQAData, llmQuestion.getAccountIdentifier());

            CustomResponse<LlmAnswer> answerCustomResponse = new CustomResponse<>(Constants.SUCCESS, "LLm answer success full",
                    LlmAnswer.builder().answer(llmSignalQAData.getAnswer())
                            .question(llmSignalQAData.getQuestion())
                            .questionId(llmSignalQAData.getQuestionId())
                            .questionTime(llmSignalQAData.getQuestionTime())
                            .conversationId(llmSignalQAData.getConversationId())
                            .conversationTitle(llmQuestion.getConversationTitle())
                            .conversationStartTime(llmQuestion.getConversationStartTime())
                            .build());

            return new ResponseEntity<>(answerCustomResponse, HttpStatus.OK);
        } catch (AuthException e) {
            healthMetrics.updateUnauthorizedRequests();
            log.error("Error while validating authorization token. QuestionId {}", llmQuestion.getQuestionId(), e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(new CustomResponse<>(Constants.FAILURE,
                    "Authorization failed", e.getMessage()));
        } catch (HandlerException ex) {
            healthMetrics.updateOpenSearchErrors(1L);
            log.error("Error while processing QA request to OS. QuestionId {}", llmQuestion.getQuestionId(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new CustomResponse<>(Constants.FAILURE,
                    "Opensearch error", ex.getSimpleMessage()));
        } catch (LLMServiceException llmServiceException) {
            log.error("Error while processing QA request to LLM. QuestionId {}", llmQuestion.getQuestionId(),
                    llmServiceException);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new CustomResponse<>(Constants.FAILURE,
                    "LLM Service api failed", llmServiceException.getSimpleMessage()));
        } catch (InvalidRequestBodyException e) {
            healthMetrics.updateErrors();
            log.error("Error while processing QA request. QuestionId {}", llmQuestion.getQuestionId(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new CustomResponse<>(Constants.FAILURE,
                    "Invalid request", e.getMessage()));
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while processing QA request. QuestionId {}", llmQuestion.getQuestionId(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new CustomResponse<>(Constants.FAILURE,
                    "Failed to get answer for the question", e.getLocalizedMessage()));
        }
    }
}

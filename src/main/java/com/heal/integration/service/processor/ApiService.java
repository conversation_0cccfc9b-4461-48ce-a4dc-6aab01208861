package com.heal.integration.service.processor;

import com.heal.integration.service.config.PropertyConfig;
import com.heal.integration.service.exceptions.LLMServiceException;
import com.heal.integration.service.pojo.LLMRequestPojo;
import com.heal.integration.service.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class ApiService {

    private final RestTemplate restTemplate;
    private final PropertyConfig propertyConfig;
    private final HealthMetrics healthMetrics;

    public ApiService(RestTemplate restTemplate, PropertyConfig propertyConfig, HealthMetrics healthMetrics) {
        this.restTemplate = restTemplate;
        this.propertyConfig = propertyConfig;
        this.healthMetrics = healthMetrics;
    }

    public ResponseEntity<String> callLLMService(LLMRequestPojo llmRequest) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<LLMRequestPojo> request = new HttpEntity<>(llmRequest, headers);
            return restTemplate.postForEntity(propertyConfig.getLlmServiceUrl(), request, String.class);
        } catch (Exception e) {
            healthMetrics.updateLLMErrors();
            log.error("Unable to reach or send the data to LLM. {}", propertyConfig.getLlmServiceUrl(), e);
            throw new LLMServiceException(e.getCause().getLocalizedMessage(), e);
        }
    }
}

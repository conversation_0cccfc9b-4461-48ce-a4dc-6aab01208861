package com.heal.integration.service.processor;

import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.heal.integration.service.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;


@Slf4j
@Service
public class EventConsumerFromQueue {
    @Autowired
    HealthMetrics healthMetrics;
    @Autowired
    private ProcessEventDetails processEventDetails;
    @Value("${spring.rabbitmq.signalMessageAIOpsQueueName:signal-messages-aiops}")
    public String signalMessageAIOpsQueueName;

    public void receiveSignalMessageAIOpsData(byte[] signalStream) {
        healthMetrics.updateRMQReads();

        SignalProtos.SignalDetails signalDetailsProto;
        try {
            try {
                signalDetailsProto = SignalProtos.SignalDetails.parseDelimitedFrom(new ByteArrayInputStream(signalStream));
            } catch (Exception e) {
                signalDetailsProto = SignalProtos.SignalDetails.parseFrom(signalStream);
            }
            log.info("Signals/Incidents received from queue {} with signalId {}", signalMessageAIOpsQueueName, signalDetailsProto.getSignalId());

            log.trace("Incidents details received from queue {}. Details: {}", signalMessageAIOpsQueueName, signalDetailsProto);

            processEventDetails.processIncidentDetailsForRCA(signalDetailsProto);
        } catch (Exception e) {
            log.error("Error processing signals data : ", e);
            healthMetrics.updateRMQReadDataErrors();
        }
    }
}
package com.heal.integration.service.processor;

import com.heal.integration.service.config.PropertyConfig;
import com.heal.integration.service.pojo.IncidentDetails;
import com.heal.integration.service.pojo.LlmSignalRCAData;
import com.heal.integration.service.repo.OpenSearchRepo;
import com.heal.integration.service.utility.CommonUtils;
import com.heal.integration.service.utility.Constants;
import com.heal.integration.service.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.Date;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR> Suman - 11-06-2024
 * Service class for pushing data to the LLm REST API.
 */
@Slf4j
@Service
public class LLmRestApiPusher {

    @Value("${llm.rest.api.pusher.max.queue.size:50}")
    private int maxQueueSize;

    private final Queue<IncidentDetails> restApiQueue = new ConcurrentLinkedQueue<>();
    private final HealthMetrics metrics;
    private final ApiService apiService;
    private final PropertyConfig propertyConfig;
    private final OpenSearchRepo openSearchRepo;
    private final CommonUtils commonUtils;

    public LLmRestApiPusher(HealthMetrics metrics, ApiService apiService, PropertyConfig propertyConfig,
                            OpenSearchRepo openSearchRepo, CommonUtils commonUtils) {
        this.apiService = apiService;
        this.metrics = metrics;
        this.propertyConfig = propertyConfig;
        this.openSearchRepo = openSearchRepo;
        this.commonUtils = commonUtils;
    }

    public void addToQueue(IncidentDetails request) {
        if (restApiQueue.size() > maxQueueSize) {
            log.error("Maximum queue limit {} breached.", maxQueueSize);
            metrics.updateErrors();
            return;
        }
        restApiQueue.offer(request);
        metrics.setRestApiQueueSize(restApiQueue.size());
    }

    @Scheduled(initialDelayString = "${llm.rest.api.pusher.schedule.initial.delay:2}",
            fixedRateString = "${llm.rest.api.pusher.schedule.interval:30}", timeUnit = TimeUnit.SECONDS)
    private void pushToLLM() {
        long st = System.currentTimeMillis();
        int queueSize = restApiQueue.size();
        try {
            while (restApiQueue.peek() != null) {
                IncidentDetails request = restApiQueue.poll();
                if (request == null) {
                    log.info("Request is empty. Nothing to push.");
                    return;
                }

                try {
                    log.debug("Pushing request to LLM API: {}", request.getLlmRequestPojo());
                    long time = System.currentTimeMillis();
                    if (request.getLlmRequestPojo().getMessages().isEmpty()) {
                        log.debug("message body for the llm is empty, so returning");
                        return;
                    }
                    // Code to push request to LLM API
                    ResponseEntity<String> response = apiService.callLLMService(request.getLlmRequestPojo());
                    long totalTime = System.currentTimeMillis() - time;
                    log.debug("total time taken to process the llm api {} is {}", propertyConfig.getLlmServiceUrl(), totalTime);
                    String body = "";
                    if (response != null && response.getStatusCode().is2xxSuccessful()) {
                        log.info("Response successfully received from LLM service for rca: {}", response);
                        body = response.getBody();
                    }

                    if (response != null && !response.getStatusCode().is2xxSuccessful()) {
                        log.error("Error occurred while calling the LLM server, response code:{}, URL:{}", response.getStatusCode(), propertyConfig.getLlmServiceUrl());
                    }
                    // make opensearch call to save response for rca data
                    processResponseToOS(request, body, totalTime);
                } catch (Exception e) {
                    log.error("Error while pushing request to LLM API: {}", e.getMessage());
                    metrics.updateErrors();
                }
            }
        } catch (Exception e) {
            log.error("Exception while pushing request to LLM API: {}", e.getMessage());
            metrics.updateErrors();
        } finally {
            log.debug("Pushed data of size {} to LLM API, time taken {} ms.", queueSize, (System.currentTimeMillis() - st));
        }
    }

    private void processResponseToOS(IncidentDetails llmRequest, String body, long totalTime) {
        try {
            long currentTime = System.currentTimeMillis();
            Date dateTime = commonUtils.getCurrentTimeInGMT(currentTime);
            LlmSignalRCAData healLLMSignalPojo = LlmSignalRCAData.builder()
                    .requestData(llmRequest.getLlmRequestPojo().getMessages().get(0).getContent())
                    .responseData(body)
                    .url(propertyConfig.getLlmServiceUrl())
                    .signalId(llmRequest.getIncidentId())
                    .uuid(UUID.randomUUID().toString())
                    .timeInGMT(dateTime.getTime())
                    .responseTime(totalTime)
                    .criteriaDetails(llmRequest.getCriteriaDetails())
                    .timestamp(commonUtils.date2GMTConversion(new Date(currentTime), Constants.TIMESTAMP_FORMAT_INDEX_PATTERN))
                    .build();
            log.debug("OS data for llm request and response is pushed to os {}", healLLMSignalPojo);
            openSearchRepo.addRCADetailsToOS(healLLMSignalPojo, llmRequest.getAccountId());
        } catch (ParseException e) {
            log.error("Error while converting date to GMT: ", e);
            metrics.updateErrors();
        } catch (Exception e) {
            log.error("Error while pushing data of rca to OS: ", e);
            metrics.updateErrors();
        }
    }
}

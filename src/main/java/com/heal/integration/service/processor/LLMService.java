package com.heal.integration.service.processor;

import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.enums.DocumentStatus;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.CompInstClusterDetails;
import com.heal.configuration.pojos.LlmQuestion;
import com.heal.configuration.pojos.opensearch.LlmSignalQAData;
import com.heal.integration.service.config.PropertyConfig;
import com.heal.integration.service.exceptions.InvalidRequestBodyException;
import com.heal.integration.service.pojo.*;
import com.heal.integration.service.pojo.topology.Edge;
import com.heal.integration.service.pojo.topology.Topology;
import com.heal.integration.service.repo.RedisRepo;
import com.heal.integration.service.utility.CommonUtils;
import com.heal.integration.service.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.StringWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Suman - 30-04-2024
 */
@Slf4j
@Service
public class LLMService {

    private final RedisRepo redisRepo;
    private final ApiService apiService;
    private final PropertyConfig propertyConfig;
    private final CommonUtils commonUtils;
    private final VelocityEngine velocityEngine;
    private final ObjectMapper objectMapper;

    public LLMService(VelocityEngine velocityEngine, CommonUtils commonUtils, PropertyConfig propertyConfig,
                      ApiService apiService, RedisRepo redisRepo, ObjectMapper objectMapper) {
        this.velocityEngine = velocityEngine;
        this.commonUtils = commonUtils;
        this.propertyConfig = propertyConfig;
        this.apiService = apiService;
        this.redisRepo = redisRepo;
        this.objectMapper = objectMapper;
    }

    public void getOutboundTopologyByService(String accountIdentifier, String serviceIdentifier, Topology topology) {
        try {
            topology.addNode(serviceIdentifier, serviceIdentifier);

            List<BasicEntity> connectionEntity = redisRepo.getConnectionDetails(accountIdentifier, serviceIdentifier);
            if (connectionEntity.isEmpty()) {
                log.trace("No connections found for account {} and service {}", accountIdentifier, serviceIdentifier);
                return;
            }

            for (BasicEntity connection : connectionEntity) {
                // rejects the node if already edge has been found out.
                Optional<Edge> s =
                        topology.getEdges().stream().filter(f -> f.getDestinationNode().getIdentifier().equals(connection.getIdentifier())
                                || f.getSourceNode().getIdentifier().equals(connection.getIdentifier())).findAny();
                if (s.isPresent()) {
                    continue;
                }
                topology.addNode(connection.getIdentifier(), connection.getName());
                topology.addConnection(serviceIdentifier, connection.getIdentifier());

                getOutboundTopologyByService(accountIdentifier, connection.getIdentifier(), topology);
            }
        } catch (Exception ex) {
            log.error("Error occurred while getting connections details for serviceId {}", serviceIdentifier, ex);
        }
    }

    // TODO : make redis call to get the kpi name and instance name if not available in the signal details. for now
    //  use identifier for kpi and instance.
    public RCASignalTemplate populateRCARequestForTemplate(SignalProtos.SignalDetails signalDetailsProto, List<String> paths) {
        List<SignalProtos.AnomalyDetail> anomalyList = signalDetailsProto.getAnomalyDetailsList();

        List<com.heal.configuration.pojos.Service> serviceList =
                redisRepo.getServiceDetails(signalDetailsProto.getAccountId());

        if (serviceList.isEmpty()) {
            log.error("No service is available for account identifier:{}", signalDetailsProto.getAccountId());
        }
        log.info("total services found in account id {} is {}.", signalDetailsProto.getAccountId(), serviceList.size());

        Map<String, String> serviceIdNameMap = serviceList
                .stream()
                .collect(Collectors.toMap(com.heal.configuration.pojos.Service::getIdentifier,
                        com.heal.configuration.pojos.Service::getName));
        log.trace("service id to name map is {}", serviceIdNameMap);


        String instanceName = signalDetailsProto.getMetadataMap().getOrDefault("InstanceName", null);
        Map<String, String> instanceIdNameMap = new HashMap<>();
        if (instanceName == null) {
            log.info("KPI Name is not available in the signal details");
            List<CompInstClusterDetails> compInstances = redisRepo.getCompInstances(signalDetailsProto.getAccountId());
            log.debug("total instances found in account id {} is {}.", signalDetailsProto.getAccountId(),
                    compInstances.size());
            instanceIdNameMap.putAll(compInstances.stream().collect(Collectors.toMap(CompInstClusterDetails::getIdentifier,
                    CompInstClusterDetails::getName)));
        }

        RCASignalTemplate.RCASignalTemplateBuilder builder = RCASignalTemplate.builder()
                .incidentIdentifier(signalDetailsProto.getSignalId())
                .topology(String.join(", ", paths));

        List<RCAAnomalyTemplate> anomalyTemplateList = anomalyList
                .stream()
                .map(anomaly ->
                        RCAAnomalyTemplate.builder()
                                .eventIdentifier(anomaly.getAnomalyId())
                                .instanceIdentifier(anomaly.getInstanceId())
                                .serviceIdentifier(anomaly.getServiceId())
                                .kpiIdentifier(anomaly.getKpiId())
                                .kpiValue(anomaly.getKpiValue())
                                .workload(anomaly.getIsWorkLoad())
                                .operationType(anomaly.getOperationType())
                                .thresholds(anomaly.getThresholdsMap())
                                .serviceName(serviceIdNameMap.getOrDefault(anomaly.getServiceId(), anomaly.getServiceId()))
                                .kpiName(signalDetailsProto.getMetadataMap().getOrDefault("KPIName", anomaly.getKpiId()))
                                .instanceName(instanceName != null ? instanceName :
                                        instanceIdNameMap.getOrDefault(anomaly.getInstanceId(), anomaly.getInstanceId()))
                                .categoryId(anomaly.getCategoryId())
                                .build()
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return builder.rcaAnomalyTemplates(anomalyTemplateList).build();
    }

    public LLMRequestPojo getLLmRequestForRCA(SignalProtos.SignalDetails signalDetailsProto, List<String> paths) {
        RCASignalTemplate rcaSignalTemplate = populateRCARequestForTemplate(signalDetailsProto, paths);
        log.debug("RCA details feed to llm {}.", rcaSignalTemplate);

        return prompt(rcaSignalTemplate, null, Collections.emptyMap());
    }

    public LlmSignalQAData llmQAServiceCall(LlmQuestion llmQuestion, Map<String, String> contextFormation) throws InvalidRequestBodyException {
        LLMRequestPojo llmRequest = prompt(null, llmQuestion, contextFormation);
        log.debug("question to llm generated is {}", llmRequest);
        long startTime = System.currentTimeMillis();
        ResponseEntity<String> response = apiService.callLLMService(llmRequest);
        long executionTime = System.currentTimeMillis() - startTime;

        log.debug("llm call for q&a execution time {}, conversation id [{}]", executionTime,
                llmQuestion.getConversationId());

        log.trace("response from llm is {}", response);

        if (response == null) {
            log.error("Null response received from LLM service for q&a");
            throw new InvalidRequestBodyException("Null response received from LLM service for q&a");
        }

        if (!response.getStatusCode().is2xxSuccessful()) {
            log.error("Error occurred while calling the LLM server, response code:{}, URL:{}", response.getStatusCode(), propertyConfig.getLlmServiceUrl());
            throw new InvalidRequestBodyException("Error occurred while calling LLM server");
        }

        String body = "";
        if (response.getStatusCode().is2xxSuccessful()) {
            try {
                log.info("Response successfully received from LLM service for q&a");
                OpenAIResponse openAIResponse = objectMapper.readValue(response.getBody(), OpenAIResponse.class);
                if (openAIResponse != null && openAIResponse.getChoices() != null && !openAIResponse.getChoices().isEmpty()) {
                    body = openAIResponse.getChoices().get(0).getMessage().getContent();
                }
            } catch (Exception e) {
                log.error("Error while parsing response from LLM service for q&a", e);
                throw new InvalidRequestBodyException("Error while parsing response from LLM service for q&a");
            }
        }


        return LlmSignalQAData.builder()
                .signalId(llmQuestion.getIncidentIdentifier())
                .conversationId(llmQuestion.getConversationId())
                .conversationTitle(llmQuestion.getConversationTitle())
                .questionId(llmQuestion.getQuestionId())
                .question(llmQuestion.getQuestion())
                .answer(body)
                .status(DocumentStatus.ACTIVE)
                .questionTime(llmQuestion.getQuestionTime())
                .responseTime(executionTime)
                .url(propertyConfig.getLlmServiceUrl())
                .uuid(llmQuestion.getUuid())
                .userId(llmQuestion.getUserId())
                .conversationStartTime(llmQuestion.getConversationStartTime())
                .timestamp(commonUtils.date2GMTConversion(new Date(llmQuestion.getQuestionTime()),
                        Constants.TIMESTAMP_FORMAT_INDEX_PATTERN))
                .build();
    }

    private LLMRequestPojo prompt(RCASignalTemplate rcaSignalTemplate, LlmQuestion llmQuestion, Map<String, String> contextFormation) {
        StringWriter writer = new StringWriter();
        if (rcaSignalTemplate != null && !rcaSignalTemplate.getRcaAnomalyTemplates().isEmpty()) {
            Template template = velocityEngine.getTemplate("llm-message.vm");
            VelocityContext context = new VelocityContext();
            context.put("rcaSignalTemplate", rcaSignalTemplate);
            template.merge(context, writer);
            log.trace("template file created with data {}", writer);
        } else if (llmQuestion != null) {
            log.info("question formation is chat is in process...");
            writer.write(llmQuestion.getQuestion());
        }

        List<Message> messages = new ArrayList<>();
        if (llmQuestion != null && contextFormation != null && !contextFormation.isEmpty()) {
            for (String role : contextFormation.keySet()) {
                Message contextPrompt = Message.builder()
                        .name("string")
                        .role(role.split("_")[0])
                        .content(role.split("_")[0].equalsIgnoreCase(Constants.ROLE_SYSTEM) ?
                                propertyConfig.getPromptMessageSystemRole() + " " + contextFormation.get(role) :
                                contextFormation.get(role))
                        .build();
                log.trace("message for context formation is {}", contextFormation);
                messages.add(contextPrompt); // add context prompt only if content is not null
            }
        }
        Message userInputPrompt = Message.builder()
                .name("string")
                .role(Constants.ROLE_USER)
                .content(writer.toString())
                .build();

        messages.add(userInputPrompt);

        return LLMRequestPojo.builder()
                .messages(messages)
                .n_predict(400)
                .stream(false)
                .temperature(0.7f)
                .stop(new String[]{"<|eot_id|>", "</s>", "Llama", "User:"})
                .repeat_last_n(256)
                .repeat_penalty(1.18f)
                .top_k(40)
                .top_p(0.95f)
                .min_p(0.05f)
                .tfs_z(1)
                .typical_p(1)
                .presence_penalty(0)
                .frequency_penalty(0)
                .mirostat(0)
                .mirostat_tau(5)
                .mirostat_eta(0.1f)
                .grammar("")
                .n_probs(0)
                .min_keep(0)
                .image_data(new String[0])
                .cache_prompt(true)
                .api_key("")
                .slot_id(0)
                .build();
    }
}

package com.heal.integration.service.scheduler;

import com.heal.integration.service.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;

@Slf4j
@Component
public class StatisticsScheduler {

    @Autowired
    private ThreadPoolTaskExecutor threadPoolExecutor;

    @Autowired
    HealthMetrics healthMetrics;

    @Scheduled(initialDelay = 1000, fixedRateString = "${health.metrics.log.interval.milliseconds:10000}")
    public void printThreadStats() {
        log.info("Worker thread pool stats [{}]", threadPoolExecutor.getThreadPoolExecutor());
        log.info("Memory statistics : ");
        printUsage(Runtime.getRuntime());
        log.info("Available Processors : {}", ManagementFactory.getOperatingSystemMXBean().getAvailableProcessors());
        log.info("System Load Average : {}", ManagementFactory.getOperatingSystemMXBean().getSystemLoadAverage());
        log.debug("Health metrics details : {}", healthMetrics.toString());
    }

    public static void printUsage(Runtime runtime) {
        long total, free, used;
        int mb = 1024*1024;

        total = runtime.totalMemory();
        free = runtime.freeMemory();
        used = total - free;
        log.info("Total Memory: {} MB", total / mb);
        log.info("Memory Used: {} MB", used / mb);
        log.info("Memory Free: {} MB", free / mb);
        log.info("Memory Percent Used: {} %", ((double)used/(double)total)*100);
        log.info("Memory Percent Free: {} %", ((double)free/(double)total)*100);
    }
}

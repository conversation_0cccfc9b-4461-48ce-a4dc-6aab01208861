package com.heal.integration.service.exceptions;

public class LLMServiceException extends RuntimeException {
    private final String message;

    public LLMServiceException(String message) {
        super(message);
        this.message = message;
    }

    public LLMServiceException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
    }

    public String getSimpleMessage() {
        return "LLMServiceException :: " + this.message;
    }
}

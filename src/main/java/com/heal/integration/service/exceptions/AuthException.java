package com.heal.integration.service.exceptions;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AuthException extends Exception {
    private final String errorMessage;
    private final int statusCode;

    public AuthException(String errorMessage, int statusCode) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.statusCode = statusCode;
    }

    public AuthException(Throwable cause, String errorMessage, int statusCode) {
        super(cause);
        this.errorMessage = errorMessage;
        this.statusCode = statusCode;
    }

    public String getSimpleMessage() {
        return this.statusCode + " :: AuthException :: " + this.errorMessage;
    }
}

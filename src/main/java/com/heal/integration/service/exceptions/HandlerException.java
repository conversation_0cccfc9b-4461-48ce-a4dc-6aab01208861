package com.heal.integration.service.exceptions;

public class HandlerException extends Exception {
    private final String errorMessage;

    public HandlerException(String message, Exception cause) {
        super(message, cause);
        this.errorMessage = message;
    }

    public HandlerException(String errorMessage) {
        super(errorMessage);
        this.errorMessage = errorMessage;
    }

    public String getSimpleMessage() {
        return "LLM-Integration-Service :: " + this.errorMessage;
    }
}
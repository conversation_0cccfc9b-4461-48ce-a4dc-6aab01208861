package com.heal.integration.service.pojo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> - 02-04-2024
 */

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LLMRequestPojo {
    private List<Message> messages;
    private boolean stream;
    private int n_predict;
    private Float temperature;
    private String[] stop;
    private int repeat_last_n;
    private Float repeat_penalty;
    private int top_k;
    private Float top_p;
    private Float min_p;
    private int tfs_z;
    private int typical_p;
    private int presence_penalty;
    private int frequency_penalty;
    private int mirostat;
    private int mirostat_tau;
    private Float mirostat_eta;
    private String grammar;
    private int n_probs;
    private int min_keep;
    private String[] image_data;
    private Boolean cache_prompt;
    private String api_key;
    private int slot_id;
}
package com.heal.integration.service.pojo.topology;

import lombok.Getter;

/**
 * <AUTHOR> - 31-05-2024
 */
public class Edge {
    @Getter
    private Service sourceNode;
    @Getter
    private Service destinationNode;

    public Edge(Service sourceNode, Service destinationNode) {
        this.sourceNode = sourceNode;
        this.destinationNode = destinationNode;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Edge edge = (Edge) obj;
        return (sourceNode.equals(edge.sourceNode) && destinationNode.equals(edge.destinationNode));
    }

    @Override
    public int hashCode() {
        return sourceNode.hashCode() + destinationNode.hashCode();
    }

    @Override
    public String toString() {
        return "Edge{" +
                "sourceNode=" + sourceNode +
                ", destinationNode=" + destinationNode +
                '}';
    }
}

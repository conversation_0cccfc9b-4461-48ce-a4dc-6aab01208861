package com.heal.integration.service.pojo.topology;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR> - 31-05-2024
 */

@Slf4j
public class Topology {
    private Map<String, Service> nodes;
    @Getter
    private Set<Edge> edges;
    @Getter
    private List<String> paths;

    public Topology() {
        this.nodes = new HashMap<>();
        this.edges = new HashSet<>();
        this.paths = new ArrayList<>();
    }

    public void addNode(String identifier, String name) {
        nodes.putIfAbsent(identifier, new Service(identifier, name));
    }

    public void addConnection(String sourceId, String destinationId) {
        Service sourceNode = nodes.get(sourceId);
        Service destinationNode = nodes.get(destinationId);

        if (sourceNode != null && destinationNode != null) {
            edges.add(new Edge(sourceNode, destinationNode));
        }
    }

    public void generateTopology() {
        Map<Service, List<Service>> adjacencyList = new HashMap<>();

        // Create adjacency list
        for (Edge edge : edges) {
            adjacencyList
                    .computeIfAbsent(edge.getSourceNode(), k -> new ArrayList<>())
                    .add(edge.getDestinationNode());
        }

        // Find all starting nodes (nodes with no incoming edges)
        Set<Service> startingNodes = new HashSet<>(nodes.values());
        for (Edge edge : edges) {
            startingNodes.remove(edge.getDestinationNode());
        }

        // Print each starting node and its paths
        for (Service startNode : startingNodes) {
            printPaths(startNode, adjacencyList, new HashSet<>(), "");
        }
    }

    private void printPaths(Service currentNode, Map<Service, List<Service>> adjacencyList, Set<Service> visited,
                            String path) {
        if (!visited.add(currentNode)) {
            return; // Prevent cycles
        }

        String newPath = path.isEmpty() ? currentNode.getName() : path + " -> " + currentNode.getName();
        List<Service> neighbors = adjacencyList.get(currentNode);

        if (neighbors == null || neighbors.isEmpty()) {
            paths.add(newPath);
            log.trace("new path is {}", newPath);
        } else {
            for (Service neighbor : neighbors) {
                printPaths(neighbor, adjacencyList, new HashSet<>(visited), newPath);
            }
        }
    }
}

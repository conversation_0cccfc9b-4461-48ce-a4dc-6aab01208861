package com.heal.integration.service.pojo.profiling;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> - 06-06-2024
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncidentProfile {
    private String incidentId;
    private long lastTimeSignalReceived;
    //TODO: Add account profile, move below ones under account object.
    List<AccountProfile> accounts;

}

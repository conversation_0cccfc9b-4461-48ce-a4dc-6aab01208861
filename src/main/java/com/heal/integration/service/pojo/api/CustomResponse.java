package com.heal.integration.service.pojo.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * <AUTHOR> - 02-05-2024
 */

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomResponse<T> {
    private String status;
    private String message;
    private T data;
    private String error;

    // Constructor for success response
    public CustomResponse(String status, String message, T data) {
        this.status = status;
        this.message = message;
        this.data = data;
    }

    // Constructor for error response
    public CustomResponse(String status, String message, String error) {
        this.status = status;
        this.message = message;
        this.error = error;
    }
}

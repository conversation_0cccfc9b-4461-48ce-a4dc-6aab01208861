package com.heal.integration.service.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR> - 29-05-2024
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RCAAnomalyTemplate {
    private String eventIdentifier; // anomalyId
    private String serviceIdentifier;
    private String serviceName;
    private String instanceIdentifier;
    private String instanceName;
    private String kpiIdentifier;
    private String kpiName;
    private boolean workload;
    private String kpiValue;
    private String operationType;
    private Map<String, Double> thresholds;
    private String severity;
    private String forensics;
    private String thresholdType;
    private String categoryId;
}

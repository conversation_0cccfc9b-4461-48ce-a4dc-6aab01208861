package com.heal.integration.service.pojo.topology;

import lombok.Getter;

/**
 * <AUTHOR> - 30-05-2024
 */

public class Service {
    @Getter
    String name;
    @Getter
    String identifier;

    public Service(String identifier, String name) {
        this.identifier = identifier;
        this.name = name;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Service other = (Service) obj;
        return this.identifier.equals(other.identifier);
    }

    @Override
    public int hashCode() {
        return this.identifier.hashCode();
    }

    public String toString() {
        return "Node{" +
                ", name=" + name +
                ", identifier=" + identifier +
                '}';
    }
}
package com.heal.integration.service.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Usage {
    private int promptTokens;
    private int completionTokens;
    private int totalTokens;
    private CompletionTokensDetails completionTokensDetails;
}
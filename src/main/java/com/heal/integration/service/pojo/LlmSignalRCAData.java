package com.heal.integration.service.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
// TODO : Move this class to configuration pojo
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LlmSignalRCAData {

    private String signalId;
    private String requestData;
    private String responseData;
    private String criteriaDetails;
    private long responseTime;
    private String url;
    private String uuid;
    private long timeInGMT;
    @JsonProperty("@timestamp")
    private String timestamp;

}
package com.heal.integration.service.pojo.profiling;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> - 09-07-2024
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountProfile {
    private String accountId;
    private Anomalies anomalies;
    private Applications applications;
    private Categories categories;
    private long severity;
    private Components components;
    private ComponentType componentType;
    private Forensics forensics;
    private Instances instances;
    private KpiAnomalies kpiAnomalies;
    private TxnAnomalies txnAnomalies;
    private Kpis kpis;
    private KpiTypes kpiTypes;
    private Services services;
}

package com.heal.integration.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableAutoConfiguration
@ComponentScan
@ServletComponentScan
@EnableScheduling
@EnableAsync
@EnableCaching
@PropertySource(value = "classpath:conf.properties")
@Slf4j
public class HealIntegrationServiceApplication {

	public static void main(String[] args) {
		log.info("LLM Integration Service is starting........");
		SpringApplication.run(HealIntegrationServiceApplication.class, args);
		log.info("LLM Integration Service started successfully.......");
	}

}

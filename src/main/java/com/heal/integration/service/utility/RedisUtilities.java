package com.heal.integration.service.utility;

import com.google.common.base.Throwables;
import com.heal.integration.service.config.PropertyConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Suman - 02-07-2024
 */

@Service
@Slf4j
public class RedisUtilities {

    private RedisTemplate<String, Object> redisTemplate;
    private PropertyConfig propertyConfig;
    private HealthMetrics healthMetrics;

    public RedisUtilities(RedisTemplate<String, Object> redisTemplate, PropertyConfig propertyConfig,
                          HealthMetrics healthMetrics) {
        this.redisTemplate = redisTemplate;
        this.propertyConfig = propertyConfig;
        this.healthMetrics = healthMetrics;
    }

    public String getKey(String key, String hashKey) throws Exception {
        try {
            log.trace("Redis GET Query details:- Key:{}, Hash key:{}", key, hashKey);

            Object data = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (data == null) {
                log.trace("Failed to get details for Redis Key:{}, Hash key:{}", key, hashKey);
                return null;
            } else {
                log.trace("Redis GET Query successful. Key:{}, Hash key:{}, Value: {}", key, hashKey, data);
            }

            return (String) data;
        } catch (Exception ex) {
            log.error("Exception encountered while getting value of Redis Key:{}, Hash key:{}. ", key, hashKey, ex);
            throw new Exception(Throwables.getRootCause(ex));
        }
    }

    public void expireKey(String key) {
        try {
            log.trace("Redis Key {} expire set for {}", key, propertyConfig.getKeyExpireInMin());
            redisTemplate.expire(key, propertyConfig.getKeyExpireInMin(), TimeUnit.MINUTES);
        } catch (Exception ex) {
            log.error("Exception encountered while expiring Redis Key:{}. ", key, ex);
        }
    }

    public void addKey(String key, String hashKey, String data) {
        try {
            if (data == null || data.trim().isEmpty()) {
                log.error("Value received to add Redis Key:{}, Hash key:{} is null/empty.", key, hashKey);
                return;
            }

            log.trace("Redis ADD Query details:- Key:{}, Hash key:{}, Value: {}", key, hashKey, data);

            String cacheData = getKey(key, hashKey);
            if (cacheData != null && cacheData.hashCode() == data.hashCode()) {
                log.debug("No change in value for the Redis Key:{}, Hash key:{}, existing value:{}, new value:{}",
                        key, hashKey, cacheData, data);
                return;
            }

            redisTemplate.opsForHash().put(key, hashKey, data);
        } catch (Exception ex) {
            log.error("Exception encountered while updating Redis Key:{}, Hash key:{} with Value:{}. ", key, hashKey, data, ex);
            healthMetrics.updateErrors();
        }
    }

    public Set<String> getKeysByPattern(String pattern) {
        try {
            log.trace("Getting Redis Keys by pattern: [{}].", pattern);
            return redisTemplate.keys(pattern);
        } catch (Exception ex) {
            log.error("Exception encountered while getting Redis Keys by pattern: [{}].", pattern, ex);
            return Collections.emptySet();
        }
    }

    public void deleteMultipleKeys(Set<String> keys) {
        try {
            log.trace("deleting Redis Keys: {}.", keys);
            redisTemplate.delete(keys);
        } catch (Exception ex) {
            log.error("Exception encountered while deleting Redis Keys: {}.", keys, ex);
        }
    }
}

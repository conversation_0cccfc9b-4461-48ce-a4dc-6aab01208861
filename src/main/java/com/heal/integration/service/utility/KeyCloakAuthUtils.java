package com.heal.integration.service.utility;


import com.appnomic.appsone.keycloak.KeyCloakSessionValidator;
import com.appnomic.appsone.model.JWTData;
import com.appnomic.appsone.util.KeyCloakConnectionSpec;
import com.appnomic.appsone.util.KeyCloakUtility;
import com.heal.integration.service.config.PropertyConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class KeyCloakAuthUtils {

    private static KeyCloakSessionValidator keyCloakSessionValidator = null;
    private static KeyCloakUtility keyCloakUtility = null;
    private final CommonUtils commonUtils;
    private final PropertyConfig propertyConfig;

    public KeyCloakAuthUtils(CommonUtils commonUtils, PropertyConfig propertyConfig) {
        this.commonUtils = commonUtils;
        this.propertyConfig = propertyConfig;
    }

    public void init() {
        try {
            if (propertyConfig.getKeycloakIp() == null || propertyConfig.getKeycloakPort() == null || propertyConfig.getKeycloakUsername() == null || propertyConfig.getKeycloakEncryptedPassword() == null) {
                log.error("Missing keycloak specific configuration in conf file.");
                System.exit(-1);
            }

            String decryptedPwd = commonUtils.getDecryptedData(propertyConfig.getKeycloakEncryptedPassword());

            log.debug("Inside Session Validator method");
            KeyCloakConnectionSpec keyCloakConnectionSpec = new KeyCloakConnectionSpec()
                    .setKeyCloakIP(propertyConfig.getKeycloakIp())
                    .setKeyCloakPort(propertyConfig.getKeycloakPort())
                    .setKeyCloakUsername(propertyConfig.getKeycloakUsername())
                    .setKeyCloakPassword(decryptedPwd)
                    .createKeyCloakConnectionSpec();

            log.debug("KeycloakSpec : " + keyCloakConnectionSpec.toString());
            keyCloakSessionValidator = new KeyCloakSessionValidator(keyCloakConnectionSpec);
            keyCloakUtility = new KeyCloakUtility(keyCloakConnectionSpec);
        } catch (Exception e) {
            log.error("Error occurred while getting key cloak connection spec.", e);
        }
    }

    public boolean isValidKey(String authKey) {

        try {
            if (keyCloakSessionValidator == null) {
                init();
            }

            boolean status = keyCloakSessionValidator.validateJwsToken(authKey);
            log.trace("Status of the token is [{}]", status);
            return status;

        } catch (Exception e) {
            log.error("Invalid token. Reason: ", e);
            return false;
        }
    }

    public String getUserIdentifier(String authToken) {
        if (authToken == null) {
            return null;
        }

        try {
            JWTData jwtData = extractUserDetails(authToken);
            return jwtData.getSub().trim();
        } catch (Exception e) {
            log.error("Exception while fetching user details from JWT. Details: ", e);
            return null;
        }
    }

    public JWTData extractUserDetails(String appToken) throws Exception {
        if (keyCloakUtility == null)
            init();

        if (keyCloakUtility == null) {
            throw new Exception("Unable to initialize the KeyCloak server. Kindly look into the " +
                    "heal-llm-integration-service logs.");
        }
        log.trace("Invoked method: extractUserDetails");
        JWTData jwtData = keyCloakUtility.extractUsername(appToken);

        if (jwtData == null) {
            throw new Exception("Unable to get the username from token. Kindly look into the " +
                    "heal-llm-integration-service logs.");
        }

        return jwtData;
    }
}

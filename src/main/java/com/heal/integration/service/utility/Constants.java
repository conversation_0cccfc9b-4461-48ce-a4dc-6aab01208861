package com.heal.integration.service.utility;

public class Constants {
    public static final boolean IS_QUEUE_DURABLE = true;
    public static final boolean IS_QUEUE_EXCLUSIVE = false;
    public static final boolean QUEUE_AUTO_DELETE = false;
    public static final String TIMESTAMP_FORMAT_INDEX_PATTERN = "yyyy-MM-dd'T'HH:mm:ss.SSSXXX";
    public static final String HEAL_LLM_SIGNAL_INDEX_PREFIX = "heal_llm_signal_rca";
    public static final String HEAL_LLM_SIGNAL_QA_INDEX_PREFIX = "heal_llm_signal_qa";
    public static final String HEAL_LLM_ANOMALY_INDEX_PREFIX = "heal_anomalies";
    public static final String SUCCESS = "SUCCESS";
    public static final String FAILURE = "FAILURE";
    public static final String VELOCITY_PROPERTY = "classpath.resource.loader.class";
    public static final String AIOPS_CRITERIA_REDIS_KEY_PATTERN = "/signals/*/aiops-criteria";
    public static final String SIGNAL_ID_REGEX_AIOPS_CRITERIA = "/signals/(.*?)/aiops-criteria";
    public static final String INDEX_PREFIX_SIGNALS = "heal_signals";
    public static final String SIGNAL_CURRENT_STATUS = "currentStatus";
    public static final String SIGNAL_CLOSED_STATUS = "CLOSED";
    public static final String SIGNAL_UPGRADED_STATUS = "UPGRADED";
    public static final String SIGNAL_ID = "_id";
    public static final String GLOBAL_ACCOUNT_IDENTIFIER = "e573f852-5057-11e9-8fd2-b37b61e52317";
    public static final String ROLE_SYSTEM = "system";
    public static final String ROLE_USER = "user";
    public static final String ROLE_ASSISTANT = "assistant";
    public static final String ACCOUNTS_ID_TENANTS = "accounts_id_tenants";
    public static final String INDEX_PREFIX_FORENSICS = "heal_forensics";
    public static final String INDEX_PREFIX_TRACES = "jaeger-span";
    public static final String EXTENSION_TYPE = "txt";
    public static final String COMMAND_OUTPUT_SPLITER = "#@#@#APPSONE_COMMAND_OUTPUT_SEPARATOR#@#@#\n";
    public static final String COMMAND_DATA_SPLITER = "#@#@#APPSONE_NEWLINE#@#@#";
    public static final String NAME_VALUE_SPLITER = ":";

}

package com.heal.integration.service.utility;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR> - 09-05-2024
 */
@Component
@Slf4j
public class CommonUtils {

    public String getDecryptedData(String encryptedData) {
        return new String(Base64.getDecoder().decode(encryptedData),
                StandardCharsets.UTF_8);
    }

    public String date2GMTConversion(Date date, String format) {
        DateFormat gmtFormat = new SimpleDateFormat(format);
        TimeZone gmtTime = TimeZone.getTimeZone("GMT");
        gmtFormat.setTimeZone(gmtTime);
        return gmtFormat.format(date);
    }


    public Date getCurrentTimeInGMT(long currentTime) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:00");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String dateTime = simpleDateFormat.format(currentTime);
        return simpleDateFormat.parse(dateTime);
    }
}

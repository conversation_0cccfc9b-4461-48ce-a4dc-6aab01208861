package com.heal.integration.service.utility;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jmx.export.annotation.ManagedAttribute;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ManagedResource(objectName="IntegrationService:name=ApplicationInfo")
public class HealthMetrics {
    @Autowired
    private ThreadPoolTaskExecutor threadPoolExecutor;

    private int rmqReadData = 0;
    private String readQueueName;
    private int redisKeysNotAvailable = 0;
    private int rmqReadDataErrors = 0;
    private long openSearchErrors = 0;
    private long redisKeysNotFound = 0;
    private long readCountViolatedEventQueue = 0;
    private long rmqReadErrors = 0;
    private long errorCount = 0;
    private long unauthorizedRequests = 0;
    private long llmErrors;
    private long openSearchRCADataDocCount;
    private long openSearchQnADocCount;
    private long openSearchClientInitializedCount;
    private int restApiQueueSize;
    private Map<String, Integer> snapshots = new HashMap<>();

    @ManagedAttribute
    public int getWorkerPoolSize() {
        return threadPoolExecutor.getPoolSize();
    }

    @ManagedAttribute
    public int getWorkerActiveSize() {
        return threadPoolExecutor.getActiveCount();
    }

    @ManagedAttribute
    public int getWorkerQueueSize() {
        return threadPoolExecutor.getThreadPoolExecutor().getQueue().size();
    }

    public void updateRMQReads() {
        rmqReadData++;
    }

    @ManagedAttribute
    public int getRMQReads() {
        return rmqReadData;
    }

    @ManagedAttribute
    public int getRmqReadDataErrors() {
        return rmqReadDataErrors;
    }

    public void updateRMQReadDataErrors() {
        rmqReadDataErrors++;
    }


    public void resetSnapshots() {
        snapshots = new HashMap<>();
    }

    public void updateSnapshots(String keyName, int value) {
        snapshots.put(keyName, snapshots.getOrDefault(keyName, 0) + value);
    }

    @ManagedAttribute
    public Map<String, Integer> getSnapshots() {
        return snapshots;
    }

    @ManagedAttribute
    public String getReadQueueName() {
        return readQueueName;
    }

    public void setReadQueueName(String readQueueName) {
        this.readQueueName = readQueueName;
    }

    @ManagedAttribute
    public int getRedisKeysNotAvailable() {
        return redisKeysNotAvailable;
    }

    public void updateRedisKeysNotAvailable() {
        this.redisKeysNotAvailable++;
    }

    @ManagedAttribute
    public long getRedisKeysNotFound() {
        return redisKeysNotFound;
    }

    public void updateRedisKeysNotFound() {
        if (redisKeysNotFound == Long.MAX_VALUE) {
            redisKeysNotFound = 0;
        }
        redisKeysNotFound++;
    }

    @ManagedAttribute
    public long getOpenSearchErrors() {
        return openSearchErrors;
    }

    public void updateOpenSearchErrors(long l) {
        if (openSearchErrors == Long.MAX_VALUE) {
            openSearchErrors = 0;
        }
        openSearchErrors += l;
    }

    @ManagedAttribute
    public long getReadCountViolatedEventQueue() {
        return readCountViolatedEventQueue;
    }

    public void updateReadCountViolatedEventQueue() {
        if (readCountViolatedEventQueue == Long.MAX_VALUE) {
            readCountViolatedEventQueue = 0;
        }
        readCountViolatedEventQueue++;
    }

    @ManagedAttribute
    public long getRmqReadErrors() {
        if (rmqReadErrors == Long.MAX_VALUE) {
            rmqReadErrors = 0;
        }
        return rmqReadErrors;
    }

    public void updateRmqReadErrors() {
        rmqReadErrors++;
    }

    @ManagedAttribute
    public long getErrorCount() {
        return errorCount;
    }

    public void updateErrors() {
        this.errorCount++;
    }

    @ManagedAttribute
    public long getUnauthorizedRequestsCount() {
        return unauthorizedRequests;
    }

    public void updateUnauthorizedRequests() {
        if (unauthorizedRequests == Long.MAX_VALUE) {
            unauthorizedRequests = 0;
        }
        unauthorizedRequests++;
    }

    @ManagedAttribute
    public long getLLMErrors() {
        return llmErrors;
    }

    public void updateLLMErrors() {
        if (llmErrors == Long.MAX_VALUE) {
            llmErrors = 0;
        }
        llmErrors++;
    }

    @ManagedAttribute
    public long getOpenSearchRCADataDocCount() {
        return openSearchRCADataDocCount;
    }

    public void updateOpenSearchRCADataDocCount() {
        this.openSearchRCADataDocCount++;
    }

    @ManagedAttribute
    public long getOpenSearchQnADocCount() {
        return openSearchQnADocCount;
    }

    public void updateOpenSearchQnADocCount() {
        this.openSearchQnADocCount++;
    }

    @ManagedAttribute
    public long getOpenSearchClientInitializedCount() {
        return openSearchClientInitializedCount;
    }

    public void updateOpenSearchClientInitializedCount() {
        this.openSearchClientInitializedCount++;
    }

    @ManagedAttribute
    public int getRestApiQueueSize() {
        return restApiQueueSize;
    }

    public void setRestApiQueueSize(int restApiQueueSize) {
        this.restApiQueueSize = restApiQueueSize;
    }

    @Override
    public String toString() {
        return "RMQ Reads count : " + getRMQReads() + ", " +
                "RMQ read errors : " + getRmqReadDataErrors() + ", " +
                "Redis keys unavailable count : " + getRedisKeysNotAvailable() + ", " +
                "Redis errors count : " + getErrorCount() + ", " +
                "Current Snapshots size : " + getSnapshots().size() + ", " +
                "Read Queue Name : " + getReadQueueName() + ", " +
                "Unauthorized requests count : " + getUnauthorizedRequestsCount() + ", " +
                "LLM Errors : " + getLLMErrors() + ", " +
                "Opensearch Client Initialized Count : " + getOpenSearchClientInitializedCount() + ", " +
                "RCA Opensearch Doc Count : " + getOpenSearchRCADataDocCount() + ", " +
                "QnA Opensearch Doc Count : " + getOpenSearchQnADocCount() + ", " +
                "Rest API Queue Job Size : " + getRestApiQueueSize();
    }
}

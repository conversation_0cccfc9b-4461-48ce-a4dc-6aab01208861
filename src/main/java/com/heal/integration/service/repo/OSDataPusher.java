package com.heal.integration.service.repo;

import com.heal.integration.service.config.OpenSearchConfig;
import com.heal.integration.service.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch.core.IndexRequest;
import org.opensearch.client.opensearch.core.IndexResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class OSDataPusher {

    @Autowired
    OpenSearchConfig openSearchConfig;
    @Autowired
    HealthMetrics metrics;

    CompletableFuture<IndexResponse> indexResponseFuture = new CompletableFuture<>();

    public <T> IndexResponse pushToOsSync(IndexRequest<T> indexRequest, String accountIdentifier, String indexName) {
        long st = System.currentTimeMillis();
        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, indexName);
            if (client == null) {
                return null;
            }
            IndexResponse indexResponse = client.index(indexRequest);
            log.debug("time taken to add the data to OS {}", System.currentTimeMillis() - st);
            return indexResponse;
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Exception while making OpenSearch connection - ", e);
        }
        return null;
    }
}

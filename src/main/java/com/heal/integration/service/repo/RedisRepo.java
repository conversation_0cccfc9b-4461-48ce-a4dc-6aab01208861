package com.heal.integration.service.repo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.*;
import com.heal.integration.service.config.cache.CacheConstants;
import com.heal.integration.service.pojo.profiling.IncidentProfile;
import com.heal.integration.service.utility.HealthMetrics;
import com.heal.integration.service.utility.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> - 21-02-2024
 */

@Slf4j
@Repository
public class RedisRepo {
    private final HealthMetrics healthMetrics;
    private final ObjectMapper objectMapper;
    private final RedisUtilities redisUtilities;

    private final String SIGNALS_KEY = "/signals/";
    private final String SIGNALS_HASHKEY = "SIGNALS_";
    private final String AIOPS_CRITERIA_KEY = "/aiops-criteria";
    private final String AIOPS_CRITERIA_HASHKEY = "_AIOPSCRITERIA";

    public RedisRepo(ObjectMapper objectMapper, HealthMetrics healthMetrics, RedisUtilities redisUtilities) {
        this.objectMapper = objectMapper;
        this.healthMetrics = healthMetrics;
        this.redisUtilities = redisUtilities;
    }

    @Cacheable(value = CacheConstants.MASTER_DATA_CACHE, key = "'/heal/types'", unless = "#result == null || #result.isEmpty()")
    public List<ViewTypes> getMstTypes() {
        try {
            String key = "/heal/types";
            String hashKey = "HEAL_TYPES";

            String mstTypes = redisUtilities.getKey(key, hashKey);

            if (mstTypes == null || mstTypes.trim().isEmpty()) {
                log.error("Master Types information unavailable for Heal.");
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return new ArrayList<>();
            }

            return objectMapper.readValue(mstTypes, new TypeReference<List<ViewTypes>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Master Types information for Heal.", e);
            return new ArrayList<>();
        }
    }

    @Cacheable(value = CacheConstants.TOPOLOGY_CACHE, key = "'/accounts/' + #accountIdentifier + '/services/' + #serviceIdentifier + '/neighbours'")
    public Set<BasicEntity> getNeighbourDetails(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/neighbours";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_NEIGHBOURS";

        try {
            String connDetails = redisUtilities.getKey(key, hashKey);
            if (connDetails == null || connDetails.trim().isEmpty()) {
                log.error("Neighbour details not found for account: [{}], service [{}]", accountIdentifier,
                        serviceIdentifier);
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return Collections.emptySet();
            }
            return objectMapper.readValue(connDetails, new TypeReference<Set<BasicEntity>>() {
            });

        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error occurred while getting neighbour details for account [{}], service [{}]. Details: ",
                    accountIdentifier, serviceIdentifier, e);
            return Collections.emptySet();
        }
    }

    @Cacheable(value = CacheConstants.TOPOLOGY_CACHE, key = "'/accounts/' + #accountIdentifier + '/services/' + #serviceIdentifier + '/connections'", unless = "#result == null || #result.isEmpty()")
    public List<BasicEntity> getConnectionDetails(String accountIdentifier, String serviceIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/connections";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_CONNECTIONS";

        try {
            String connDetails = redisUtilities.getKey(key, hashKey);
            if (connDetails == null || connDetails.trim().isEmpty()) {
                log.warn("Connection details not found for account: [{}], service [{}]", accountIdentifier, serviceIdentifier);
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(connDetails, new TypeReference<List<BasicEntity>>() {
            });
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error occurred while getting connection details for account [{}], service [{}]. Details: ", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

    @Cacheable(value = CacheConstants.ACCOUNT_CACHE, key = "'/accounts'", unless = "#result == null || #result.isEmpty()")
    public List<Account> getAccounts() {
        String key = "/accounts";
        String hashKey = "ACCOUNT_DATA";

        try {
            String accDetails = redisUtilities.getKey(key, hashKey);
            if (accDetails == null || accDetails.trim().isEmpty()) {
                log.error("Accounts details unavailable in redis cache. ");
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(accDetails, new TypeReference<List<Account>>() {
            });
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Exception encountered while getting key '{}': {}", key, hashKey, e);
            return Collections.emptyList();
        }
    }

    @Cacheable(value = CacheConstants.ACCOUNT_CACHE, key = "'/accounts/' + #accIdentifier + '/services'", unless = "#result == null || #result.isEmpty()")
    public List<Service> getServiceDetails(String accIdentifier) {
        String key = "/accounts/" + accIdentifier + "/services";
        String hashKey = "ACCOUNTS_" + accIdentifier + "_SERVICES";

        try {
            String obj = redisUtilities.getKey(key, hashKey);
            if (obj == null || obj.trim().isEmpty()) {
                log.error("Service details unavailable in redis cache for account identifier {}.", accIdentifier);
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return Collections.emptyList();
            }

            List<Service> serviceList = objectMapper.readValue(obj, new TypeReference<List<Service>>() {
            });

            if (serviceList.isEmpty()) {
                log.error("No Service is available for account identifier:{}", accIdentifier);
                return Collections.emptyList();
            }
            return serviceList;
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to service details for account identifier {}", accIdentifier, e);
            healthMetrics.updateErrors();
            return Collections.emptyList();
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error occurred while getting service details, account identifier:{}.", accIdentifier, e);
            return Collections.emptyList();
        }
    }

    @Cacheable(value = CacheConstants.ACCOUNT_CACHE, key = "'/accounts/' + #accountIdentifier + '/instances'", unless = "#result == null || #result.isEmpty()")
    public List<CompInstClusterDetails> getCompInstances(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/instances";
        String hashKey = "ACCOUNTS" + "_" + accountIdentifier + "_INSTANCES";

        try {
            String compInstancesObj = redisUtilities.getKey(key,hashKey);
            if (compInstancesObj == null || compInstancesObj.trim().isEmpty()) {
                log.error("Component instance details not found for account: [{}]", accountIdentifier);
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(compInstancesObj, new TypeReference<List<CompInstClusterDetails>>() {
            });
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error occurred while getting component instance details for account [{}]. Details: ", accountIdentifier, e);
            return Collections.emptyList();
        }
    }

    public User getUser(String userIdentifier) {
        String key = "/users/" + userIdentifier;
        String hashKey = "USERS_" + userIdentifier;

        try {
            String userDetails = redisUtilities.getKey(key, hashKey);
            if (userDetails == null || userDetails.trim().isEmpty()) {
                log.warn("Null/Empty value found for key {} in redis.", key);
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return null;
            }

            return objectMapper.readValue(userDetails, new TypeReference<User>() {
            });
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Exception encountered while getting user details from key:{}, hashKey:{}", key, hashKey, e);
            return null;
        }
    }

    public void saveIncidentCriteria(String signalsId, IncidentProfile newProfile) {
        String key = SIGNALS_KEY + signalsId + AIOPS_CRITERIA_KEY;
        String hashKey = SIGNALS_HASHKEY + signalsId + AIOPS_CRITERIA_HASHKEY;
        try {
            redisUtilities.addKey(key, hashKey, objectMapper.writeValueAsString(newProfile));
        } catch (Exception e) {
            log.error("Error occurred while saving Signal Criteria information for incident {}", signalsId, e);
        }
    }

    public IncidentProfile getIncidentCriteria(String signalsId, IncidentProfile newProfile) {
        String key = SIGNALS_KEY + signalsId + AIOPS_CRITERIA_KEY;
        String hashKey = SIGNALS_HASHKEY + signalsId + AIOPS_CRITERIA_HASHKEY;
        try {
            Object obj = redisUtilities.getKey(key, hashKey);
            if (obj == null) {
                log.warn("Null value found for key {} in redis.", key);
                saveIncidentCriteria(signalsId, newProfile);
                return newProfile;
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<IncidentProfile>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Signal Criteria information for incident {}.", signalsId, e);
        }
        return newProfile;
    }

    public IncidentProfile getIncidentCriteria(String signalsId) {
        String key = SIGNALS_KEY + signalsId + AIOPS_CRITERIA_KEY;
        String hashKey = SIGNALS_HASHKEY + signalsId + AIOPS_CRITERIA_HASHKEY;
        try {
            Object obj = redisUtilities.getKey(key, hashKey);
            if (obj == null) {
                log.warn("Incident profile unavailable for incidentId {}", signalsId);
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return null;
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<IncidentProfile>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Signal Criteria information for incident {}.", signalsId, e);
        }
        return null;
    }

    @Cacheable(value = CacheConstants.MASTER_DATA_CACHE, key = "'/heal/index/zones'", unless = "#result == null || #result.isEmpty()")
    public List<OSIndexZoneDetails> getHealIndexZones() {
        String key = "/heal/index/zones";
        String hashKey = "HEAL_INDEX_ZONES";

        try {
            String healZones = redisUtilities.getKey(key, hashKey);
            if (healZones == null || healZones.trim().isEmpty()) {
                log.error("Heal opensearch index to zone mapping unavailable");
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(healZones, new TypeReference<List<OSIndexZoneDetails>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Heal opensearch index to zone mapping.", e);
            return Collections.emptyList();
        }
    }

    @Cacheable(value = CacheConstants.ACCOUNT_CACHE, key = "'/accounts/' + #accountIdentifier", unless = "#result == null")
    public Account getAccountDetails(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier;
        String hashKey = "ACCOUNT_DATA_" + accountIdentifier;

        try {
            Object obj = redisUtilities.getKey(key, hashKey);
            if (obj == null) {
                log.error("Account details unavailable for account identifier [{}]", accountIdentifier);
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<Account>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting to account details for account [{}]. ", accountIdentifier, e);
            return null;
        }
    }

    @Cacheable(value = CacheConstants.MASTER_DATA_CACHE, key = "'/tenants/' + #tenantIdentifier + '/opensearch'", unless = "#result == null || #result.isEmpty()")
    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String tenantIdentifier) {
        String key = "/tenants/" + tenantIdentifier + "/opensearch";
        String hashKey = "TENANTS_" + tenantIdentifier + "_OPENSEARCH";

        try {
            Object obj = redisUtilities.getKey(key, hashKey);
            if (obj == null) {
                log.error("Tenant details unavailable for tenant identifier [{}]", tenantIdentifier);
                healthMetrics.updateRedisKeysNotFound();
                healthMetrics.updateSnapshots(key, 1);
                return new ArrayList<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<List<TenantOpenSearchDetails>>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting to tenant opensearch mapping details for tenant identifier [{}]. ", tenantIdentifier, e);
            return new ArrayList<>();
        }
    }

    public Account getAccount(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier;
        String hashKey = "ACCOUNT_DATA_" + accountIdentifier;
        try {
            String accountDetail = redisUtilities.getKey(key, hashKey);
            if (accountDetail == null || accountDetail.trim().isEmpty()) {
                log.error("Account details unavailable for identifier [{}]", accountIdentifier);
                return null;
            }
            return objectMapper.readValue(accountDetail, new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting account details for account [{}]. Details: ", accountIdentifier, e);
            return null;
        }
    }
}

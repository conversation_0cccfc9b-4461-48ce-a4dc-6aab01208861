package com.heal.integration.service.repo.cache;

import com.heal.integration.service.config.PropertyConfig;
import com.heal.integration.service.config.cache.CacheConstants;
import com.heal.integration.service.pojo.profiling.AccountProfile;
import com.heal.integration.service.pojo.profiling.IncidentProfile;
import com.heal.integration.service.repo.RedisRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Service class for handling time-based checks and caching of incident profiles.
 */
@Service
@Slf4j
public class IncidentProfileCacheService {

    private PropertyConfig propertyConfig;
    private CacheManager cacheManager;
    private RedisRepo redisRepo;

    /**
     * Constructor for TimeCheckService.
     *
     * @param propertyConfig the property configuration
     */
    public IncidentProfileCacheService(PropertyConfig propertyConfig, CacheManager cacheManager, RedisRepo redisRepo) {
        this.propertyConfig = propertyConfig;
        this.cacheManager = cacheManager;
        this.redisRepo = redisRepo;
    }

    /**
     * @param newProfile the new incident profile to cache.
     * @return the incident profile, either retrieved from cache or newly stored
     */
    @Cacheable(value = CacheConstants.INCIDENT_PROFILE_CACHE, key = "#newProfile.incidentId")
    public IncidentProfile getLastCacheIncidentProfile(IncidentProfile newProfile) {
        return redisRepo.getIncidentCriteria(newProfile.getIncidentId(), newProfile);
    }

    /**
     * Updates the incident profile in the cache.
     *
     * @param incidentProfile the incident profile to be updated in the cache
     * @return the updated incident profile
     */
    @CachePut(value = CacheConstants.INCIDENT_PROFILE_CACHE, key = "#incidentProfile.incidentId")
    public IncidentProfile updateIncidentProfile(IncidentProfile incidentProfile) {
        return incidentProfile;
    }

    /**
     * @param newProfile   the new incident profile
     * @param cacheProfile the cached incident profile
     * @return true if the time difference is greater than the specified number of minutes, false otherwise
     */
    public boolean isTimeDifferenceGreaterThanNMinutes(IncidentProfile newProfile, IncidentProfile cacheProfile) {
        long millis = newProfile.getLastTimeSignalReceived() - cacheProfile.getLastTimeSignalReceived();
        log.debug("local cache time for signal id {} with cache time {}, compare time {} and diff is {}", cacheProfile.getIncidentId(),
                cacheProfile.getLastTimeSignalReceived(), newProfile.getLastTimeSignalReceived(), millis);
        boolean isCachedProfileTimeLapsed = millis >= propertyConfig.getSuppressIncidentCriteria() * 60 * 1000;
        if (isCachedProfileTimeLapsed) {
            cacheProfile.setLastTimeSignalReceived(newProfile.getLastTimeSignalReceived());
        }
        return isCachedProfileTimeLapsed;
    }

    /**
     * @param llm     the identifier for the LLM
     * @return the last call time for the LLM, either retrieved from cache or the current system time
     */
    @Cacheable(value = CacheConstants.INCIDENT_PROFILE_CACHE, key = "#llm")
    public long getLastLLMCallTime(String llm) {
        return System.currentTimeMillis();
    }

    @CacheEvict(value = CacheConstants.INCIDENT_PROFILE_CACHE, key = "#llm")
    public void evictCache(String llm) {
        // This method will only evict the cache if isReset == true
        log.info("Cache evicted for key: {}", llm);
    }

    /**
     * Checks if the IncidentProfile is cached with the given signal ID.
     * @param redisProfile the IncidentProfile retrieved from Redis
     * @param newProfile the IncidentProfile to check
     * @param accountId the account ID
     * @return true if the IncidentProfile is cached, false otherwise
     */
    public boolean isIncidentProfileCached(IncidentProfile redisProfile, IncidentProfile newProfile, String accountId) {
        boolean isIncidentProfileRedisCached = (redisProfile != null);

        boolean isIncidentProfileLocalCached =
                ((cacheManager.getCache(CacheConstants.INCIDENT_PROFILE_CACHE) != null) && (Objects.requireNonNull(cacheManager.getCache(CacheConstants.INCIDENT_PROFILE_CACHE)).get(newProfile.getIncidentId()) != null));

        log.debug("Profile cached in redis:{}, local cache:{} for incident id {} with account id {}",
                isIncidentProfileRedisCached, isIncidentProfileLocalCached, newProfile.getIncidentId(), accountId);

        return isIncidentProfileLocalCached || isIncidentProfileRedisCached;
    }

    public boolean updateServices(Map<String, AccountProfile> accountCacheMap, IncidentProfile newProfile, StringBuilder criteriaDetails) {
        if (newProfile == null || accountCacheMap == null) {
            return false;
        }
        List<AccountProfile> newAccount = newProfile.getAccounts();

        log.info("Comparing old profile with new profile for services");
        Map<String, Boolean> accountNewMap = new HashMap<>();

        newAccount.forEach(account -> {
            AccountProfile cacheAccount = accountCacheMap.getOrDefault(account.getAccountId(), null);
            if (cacheAccount != null && !cacheAccount.getServices().getNames().containsAll(account.getServices().getNames())) {
                long cacheCount = cacheAccount.getServices().getCount();

                cacheAccount.getServices().getNames().addAll(account.getServices().getNames());
                cacheAccount.getServices().setCount(cacheAccount.getServices().getNames().size());

                criteriaDetails.append(String.format("Number of services changed from %s to %s. ",
                        cacheCount, cacheAccount.getServices().getCount()));
                accountNewMap.put(account.getAccountId(), true);
                log.info("Old profile don't have all the services from new profile");
            } else {
                log.info("Old profile has same services as new profile");
            }
        });
        return !accountNewMap.isEmpty();
    }

    public boolean updateCategories(Map<String, AccountProfile> cacheProfile, IncidentProfile newProfile, StringBuilder criteriaDetails) {
        if (newProfile == null || cacheProfile == null) {
            return false;
        }
        List<AccountProfile> newAccount = newProfile.getAccounts();
        Map<String, Boolean> accountNewMap = new HashMap<>();

        log.info("Comparing old profile with new profile for services");

        newAccount.forEach(account -> {
            AccountProfile cacheAccount = cacheProfile.getOrDefault(account.getAccountId(), null);
            if (cacheAccount != null && !cacheAccount.getCategories().getNames().containsAll(account.getCategories().getNames())) {
                long cacheCount = cacheAccount.getCategories().getCount();

                cacheAccount.getCategories().getNames().addAll(account.getCategories().getNames());
                cacheAccount.getCategories().setCount(cacheAccount.getCategories().getNames().size());

                criteriaDetails.append(String.format("Number of categories changed from %s to %s. ",
                        cacheCount, cacheAccount.getCategories().getCount()));
                accountNewMap.put(account.getAccountId(), true);
                log.info("Old profile don't have all the categories from new profile");
            } else {
                log.info("Old profile has same categories as new profile");
            }
        });

        return !accountNewMap.isEmpty();
    }

    public boolean updateSeverity(Map<String, AccountProfile> cacheProfile, IncidentProfile newProfile, StringBuilder criteriaDetails) {
        if (cacheProfile == null || newProfile == null) {
            return false;
        }
        List<AccountProfile> newAccount = newProfile.getAccounts();
        Map<String, Boolean> accountNewMap = new HashMap<>();

        log.info("Comparing old profile with new profile for severity");

        newAccount.forEach(account -> {
            AccountProfile cacheAccount = cacheProfile.get(account.getAccountId());
            if (cacheAccount != null && cacheAccount.getSeverity() != account.getSeverity()) {
                criteriaDetails.append(String.format("Severity changed from %s to %s. ",
                        cacheAccount.getSeverity(), account.getSeverity()));
                cacheAccount.setSeverity(account.getSeverity());
                accountNewMap.put(account.getAccountId(), true);
                log.info("Old profile don't have same severity as new profile");
            } else {
                log.info("Old profile has same severity as new profile");
            }
        });
        return !accountNewMap.isEmpty();
    }
}

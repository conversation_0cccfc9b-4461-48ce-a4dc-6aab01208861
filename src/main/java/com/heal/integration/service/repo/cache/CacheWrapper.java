package com.heal.integration.service.repo.cache;

import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.TenantDetails;
import com.heal.configuration.pojos.TenantOpenSearchDetails;
import com.heal.integration.service.repo.RedisRepo;
import com.heal.integration.service.utility.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;


@Component
@Repository
@Slf4j
public class CacheWrapper {
    @Autowired
    RedisRepo redisRepo;


    @Cacheable(value = Constants.ACCOUNTS_ID_TENANTS, key = "#accountIdentifier",
            condition = "#accountIdentifier != null && #accountIdentifier.length() > 0", unless = "#result == null || #result.isEmpty()")
    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String accountIdentifier) {
        log.info("Populating tenant information for account: {}", accountIdentifier);

        Account account = redisRepo.getAccount(accountIdentifier);
        if (account == null) {
            log.error("Account details not found for accountIdentifier: {}", accountIdentifier);
            return null;
        }

        TenantDetails tenantDetails = account.getTenantDetails();
        if (tenantDetails == null) {
            log.error("Tenant details not found for accountIdentifier: {}", accountIdentifier);
            return null;
        }

        List<TenantOpenSearchDetails> tenantOpenSearchDetailsList =
                redisRepo.getTenantOpenSearchDetails(tenantDetails.getTenantIdentifier());

        if (tenantOpenSearchDetailsList.isEmpty()) {
            log.error("OpenSearch mapping not found for tenantId: {}, account:{}", tenantDetails.getTenantId(), accountIdentifier);
            return null;
        }

        return tenantOpenSearchDetailsList;
    }
}

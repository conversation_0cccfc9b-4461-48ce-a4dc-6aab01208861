package com.heal.integration.service.repo;

import com.appnomic.appsone.opeasearchquery.enumerations.Aggregations;
import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.Documents;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.AggregationQuery;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.appnomic.appsone.opeasearchquery.results.TabularResults;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.LlmQuestion;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.ForensicPojo;
import com.heal.configuration.pojos.opensearch.LlmSignalQAData;
import com.heal.configuration.util.DateHelper;
import com.heal.integration.service.config.OpenSearchConfig;
import com.heal.integration.service.config.PropertyConfig;
import com.heal.integration.service.exceptions.HandlerException;
import com.heal.integration.service.pojo.LlmSignalRCAData;
import com.heal.integration.service.utility.Constants;
import com.heal.integration.service.utility.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch.core.IndexRequest;
import org.opensearch.client.opensearch.core.IndexResponse;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Suman - 02-05-2024
 */

@Repository
@Slf4j
public class OpenSearchRepo {
    private final HealthMetrics healthMetrics;
    private final OSDataPusher osDataPusher;
    private final ObjectMapper objectMapper;
    private final OpenSearchConfig openSearchConfig;
    private final PropertyConfig propertyConfig;

    public OpenSearchRepo(HealthMetrics healthMetrics, OSDataPusher osDataPusher, ObjectMapper objectMapper,
                          OpenSearchConfig openSearchConfig, PropertyConfig propertyConfig) {
        this.healthMetrics = healthMetrics;
        this.osDataPusher = osDataPusher;
        this.objectMapper = objectMapper;
        this.openSearchConfig = openSearchConfig;
        this.propertyConfig = propertyConfig;
    }

    public void addRCADetailsToOS(LlmSignalRCAData healLLMSignalPojo, String accountIdentifier) throws HandlerException {
        String indexDate = DateHelper.getDailyDatesAsString(healLLMSignalPojo.getTimeInGMT(), healLLMSignalPojo.getTimeInGMT()).get(0);
        String indexName = Constants.HEAL_LLM_SIGNAL_INDEX_PREFIX + "_" + accountIdentifier.toLowerCase() + "_" + indexDate;
        try {
            log.info("RCA data to be pushed to index {}", indexName);

            IndexResponse indexResponse = osDataPusher.pushToOsSync(new IndexRequest.Builder<LlmSignalRCAData>().index(indexName).document(healLLMSignalPojo).build(),
                    accountIdentifier, Constants.HEAL_LLM_SIGNAL_INDEX_PREFIX);
            if (indexResponse == null) {
                healthMetrics.updateErrors();
                log.error("Failures during adding rca data to OS.");
                return;
            }
            if (indexResponse.shards().successful().intValue() > 0) {
                healthMetrics.updateOpenSearchRCADataDocCount();
                log.debug("Req and resp details for the rca is added OS");
            } else {
                healthMetrics.updateErrors();
                log.error("Failures during adding rca data to OS. Index name :{}, data {}", indexName, healLLMSignalPojo);
            }
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while writing llm RCA request/response details to OpenSearch. Details: ", e);
            throw new HandlerException("Error while writing llm RCA request/response details to OpenSearch");
        }
    }

    public void addQAResponseToOS(LlmSignalQAData llmSignalQAData, String accountIdentifier) throws HandlerException {
        String indexDate = DateHelper.getDailyDatesAsString(llmSignalQAData.getQuestionTime(), llmSignalQAData.getQuestionTime()).get(0);
        String indexName = Constants.HEAL_LLM_SIGNAL_QA_INDEX_PREFIX + "_" + accountIdentifier.toLowerCase() + "_" + indexDate;
        try {
            log.info("Q&A data to be pushed to index {}", indexName);
            IndexResponse indexResponse = osDataPusher.pushToOsSync(new IndexRequest.Builder<LlmSignalQAData>().index(indexName).document(llmSignalQAData).build(),
                    accountIdentifier, Constants.HEAL_LLM_SIGNAL_QA_INDEX_PREFIX);
            if (indexResponse == null) {
                log.error("Error while inserting data for QnA data. Index name :{}, data {}", indexName, llmSignalQAData);
                return;
            }
            if (indexResponse.shards().successful().intValue() > 0) {
                healthMetrics.updateOpenSearchRCADataDocCount();
                log.debug("Req and resp details for the rca is added OS");
            } else {
                healthMetrics.updateErrors();
                log.error("Failures during adding rca data to OS. Index name :{}, data {}", indexName, llmSignalQAData);
            }
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while writing llm q & a request/response details to OpenSearch. Details: ", e);
            throw new HandlerException("Error while writing llm q & a request/response details to OpenSearch", e);
        }
    }

    public Set<String> getSignalsWithClosedStatus(long fromTime, long toTime, Set<String> incidentIds,
                                                  String accountId, String status) {
        String indexPrefix = Constants.INDEX_PREFIX_SIGNALS + "_" + accountId.toLowerCase();

        try {
            List<String> indexNames = new ArrayList<>();
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountId,
                    Constants.INDEX_PREFIX_SIGNALS);
            if (client == null) {
                return new HashSet<>();
            }

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair(Constants.SIGNAL_CURRENT_STATUS, status));

            List<String> groupByColumns = new ArrayList<>();
            groupByColumns.add(Constants.SIGNAL_ID);

            List<NameValuePair> matchAnyFields = new ArrayList<>();
            incidentIds.forEach(ids -> matchAnyFields.add(new NameValuePair("_id", ids)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .groupByColumns(Optional.of(groupByColumns))
                    .isTimeSeriesData(false)
                    .epochStartTimeFieldName("startedTime")
                    .epochFromDate(fromTime)
                    .epochEndTimeFieldName("updatedTime")
                    .epochToDate(toTime)
                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
                        add(matchAnyFields);
                    }}))
                    .matchAllFields(Optional.of(matchAllFields)).build();

            log.debug("OS query for fetching signal ids with closed status: {}", queryOptions);

            TabularResults tabularResults = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, client);

            if (tabularResults.getRowResults() == null || tabularResults.getRowResults().isEmpty()) {
                return Collections.emptySet();
            }
            return tabularResults.getRowResults().stream()
                    .flatMap(rowResult -> rowResult.getListOfRows().stream())
                    .map(u -> String.format("/signals/%s/aiops-criteria", u.getColumnValue()))
                    .collect(Collectors.toSet());

        } catch (Exception ex) {
            healthMetrics.updateErrors();
            log.error("Error in getting aggregated data from index:{}", indexPrefix, ex);
            return new HashSet<>();
        }
    }

    public LlmSignalRCAData getLatestRCABySignal(LlmQuestion llmQuestion) {
        String indexPrefix = Constants.HEAL_LLM_SIGNAL_INDEX_PREFIX + "_" + llmQuestion.getAccountIdentifier().toLowerCase() + "_*";

        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(llmQuestion.getAccountIdentifier(),
                    Constants.INDEX_PREFIX_SIGNALS);
            if (client == null) {
                return null;
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexPrefix);

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("signalId", llmQuestion.getIncidentIdentifier()));
            matchAllFields.add(new NameValuePair("uuid", llmQuestion.getUuid()));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchAllFields))
                    .fetchAllRecords(true)
                    .epochFieldName("@timestamp")
                    .numberOfRawRecords(1)
                    .build();

            log.debug("OS query for fetching rca for signal id and uuid is : {}", queryOptions);
            RawDocumentResults rawDocumentResults = OpenSearchQueryHelper.getRawDocuments(queryOptions, client);

            if (rawDocumentResults != null && rawDocumentResults.getDocuments() != null && !rawDocumentResults.getDocuments().isEmpty()) {
                try {
                    LlmSignalRCAData llmSignalRCAData =
                            objectMapper.readValue(rawDocumentResults.getDocuments().get(0).getSource(), LlmSignalRCAData.class);
                    if (llmSignalRCAData != null) {
                        log.debug("rca details for signal id {} and uuid {} is {}",
                                llmQuestion.getIncidentIdentifier(), llmQuestion.getUuid(), llmSignalRCAData);
                        return llmSignalRCAData;
                    }
                } catch (JsonProcessingException jpe) {
                    log.error("Error while converting llm rca data to json format", jpe);
                }
            }
            log.debug("no rca details found for the signal id {} and uuid {}", llmQuestion.getIncidentIdentifier(),
                    llmQuestion.getUuid());
            return null;
        } catch (Exception ex) {
            healthMetrics.updateErrors();
            log.error("Error in getting  data from index:{}", indexPrefix, ex);
        }
        return null;
    }

    public void getLatestTopNQandAByUUID(LlmQuestion llmQuestion, Map<String, String> contextFormation) {
        String indexPrefix = Constants.HEAL_LLM_SIGNAL_QA_INDEX_PREFIX + "_" + llmQuestion.getAccountIdentifier().toLowerCase() + "_*";
        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(llmQuestion.getAccountIdentifier(),
                    Constants.INDEX_PREFIX_SIGNALS);
            if (client == null) {
                return;
            }
            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexPrefix);

            List<NameValuePair> matchAllFields = new ArrayList<>();
            matchAllFields.add(new NameValuePair("uuid", llmQuestion.getUuid()));
            matchAllFields.add(new NameValuePair("signalId", llmQuestion.getIncidentIdentifier()));
            matchAllFields.add(new NameValuePair("conversationId", llmQuestion.getConversationId()));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchAllFields))
                    .numberOfRawRecords(propertyConfig.getMaximumQuestionAndAnswerOS())
                    .epochFieldName("@timestamp")
                    .build();

            log.debug("OS query for fetching qa for signal id and uuid is : {}", queryOptions);
            RawDocumentResults rawDocumentResults = OpenSearchQueryHelper.getRawDocuments(queryOptions, client);
            if (rawDocumentResults != null && rawDocumentResults.getDocuments() != null && !rawDocumentResults.getDocuments().isEmpty()) {
                Collections.reverse(rawDocumentResults.getDocuments());
                for (Documents document : rawDocumentResults.getDocuments()) {
                    try {
                        LlmSignalQAData llmSignalQAData = objectMapper.readValue(document.getSource(), LlmSignalQAData.class);
                        if (llmSignalQAData != null) {
                            log.trace("qa details for signal id {} and uuid {} is {}",
                                    llmQuestion.getIncidentIdentifier(), llmQuestion.getUuid(), llmSignalQAData);
                            contextFormation.put(Constants.ROLE_USER + "_" + llmSignalQAData.getQuestionId(),
                                    llmSignalQAData.getQuestion());
                            contextFormation.put(Constants.ROLE_ASSISTANT + "_" + llmSignalQAData.getQuestionId(),
                                    llmSignalQAData.getAnswer());
                        }
                    } catch (JsonProcessingException jpe) {
                        log.error("Error while converting llm qa data to json format", jpe);
                    }
                }
            }
        } catch (Exception ex) {
            healthMetrics.updateErrors();
            log.error("Error in getting  data from index:{}", indexPrefix, ex);
        }
    }

    public List<Anomalies> getAllRecentUniqueAnomaliesByService_Instance_Txn_Kpi_Attribute(String accountIdentifier,
                                                                                           String signalIds, boolean isWorkload) {
        String indexPrefix = Constants.HEAL_LLM_ANOMALY_INDEX_PREFIX + "_" + accountIdentifier.toLowerCase() + "_*";
        List<Anomalies> anomalyList = new ArrayList<>();
        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier,
                    Constants.HEAL_LLM_ANOMALY_INDEX_PREFIX);
            if (client == null) {
                log.error("error getting opensearch client details");
                return new ArrayList<>();
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(indexPrefix);
            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("signalIds", signalIds));

            List<String> columns = new ArrayList<>();
            columns.add("serviceId");
            if (isWorkload) {
                columns.add("transactionId");
            } else {
                columns.add("instanceId");
            }
            columns.add("kpiId");
            columns.add("kpiAttribute");

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(new AggregationQuery(Aggregations.TOP_HITS, "@timestamp", null, 1));

            QueryOptions queryOptions = QueryOptions.builder()
                    .epochFieldName("@timestamp")
                    .indexNames(indexNames)
                    .fetchAllRecords(false)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .aggregationQueries(Optional.of(aggregationQueries))
                    .groupByColumns(Optional.of(columns))
                    .build();

            log.debug("OS query for all anomalies: {}", queryOptions);

            TabularResults tabularResults = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, client);
            if (tabularResults.getRowResults() != null && !tabularResults.getRowResults().isEmpty()) {
                for (TabularResults.ResultRow resultRow : tabularResults.getRowResults()) {
                    for (TabularResults.ResultRow.ResultRowColumn column : resultRow.getListOfRows()) {
                        if (column.getColumnName().equalsIgnoreCase("TOP_HITS: @timestamp")) {
                            Anomalies anomalies = objectMapper.readValue(column.getColumnValue(), Anomalies.class);
                            anomalyList.add(anomalies);
                        }
                    }
                }
            }
            log.debug("Total anomalies for signal ids {} is {}", signalIds, anomalyList.size());
            return anomalyList;
        } catch (Exception ex) {
            healthMetrics.updateErrors();
            log.error("Error in getting  data from index:{}", indexPrefix, ex);
            return new ArrayList<>();
        }
    }

    public List<ForensicPojo> getForensicDataByInstanceCategoryAndTriggerTime(String accountIdentifier,
                                                                              Set<String> compInstanceId,
                                                                              Set<String> categoryId,
                                                                              long forensicTriggerFromTime,
                                                                              long forensicTriggerToTime) {
        String indexName = Constants.INDEX_PREFIX_FORENSICS + "_" + accountIdentifier.toLowerCase();
        ForensicPojo forensicPojo;
        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, Constants.INDEX_PREFIX_FORENSICS);

            if (client == null) {
                log.error("error getting opensearch client details");
                return new ArrayList<>();
            }

            List<String> dateList = DateHelper.getWeeksAsString(forensicTriggerFromTime, forensicTriggerToTime);
            List<String> indexNames = new ArrayList<>();
            dateList.forEach(date -> indexNames.add(indexName + "_" + date));


            List<NameValuePair> matchAnyFields1 = new ArrayList<>();

            compInstanceId.forEach(instanceId -> matchAnyFields1.add(new NameValuePair("instanceId", instanceId)));

            List<NameValuePair> matchFields = new ArrayList<>();
            categoryId.forEach(category -> matchFields.add(new NameValuePair("categoryId", category)));

            List<String> columns = new ArrayList<>();
            columns.add("instanceId");

            List<AggregationQuery> aggregationQueries = new ArrayList<>();
            aggregationQueries.add(new AggregationQuery(Aggregations.TOP_HITS, "@timestamp", null, 1));


            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("forensicTriggerTime")
                    .epochFromDate(forensicTriggerFromTime)
                    .epochToDate(forensicTriggerToTime)
                    .isTimeSeriesData(false)
                    .aggregationQueries(Optional.of(aggregationQueries))
                    .groupByColumns(Optional.of(columns))
                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
                        add(matchAnyFields1);
                        add(matchFields);
                    }}))
                    .useTermsMatchAnyQuery(true)
                    .build();

            log.debug("OS query for fetching forensic data: {}", queryOptions);
            List<ForensicPojo> forensicPojos = new ArrayList<>();
            TabularResults results = OpenSearchQueryHelper.tabularOpenSearchQuery(queryOptions, client);
            if (results.getRowResults() != null && !results.getRowResults().isEmpty()) {
                for (TabularResults.ResultRow resultRow : results.getRowResults()) {
                    for (TabularResults.ResultRow.ResultRowColumn column : resultRow.getListOfRows()) {
                        if (column.getColumnName().equalsIgnoreCase("TOP_HITS: @timestamp")) {
                            forensicPojo = objectMapper.readValue(column.getColumnValue(), ForensicPojo.class);
                            forensicPojos.add(forensicPojo);
                            log.trace("Forensic details: {}", column.getColumnValue());
                        }
                    }
                }
            }
            return forensicPojos;
        } catch (Exception exception) {
            healthMetrics.updateErrors();
            log.error("Error in getting doc from index {} : ", indexName, exception);
            return new ArrayList<>();
        }
    }
}

# ==========================================================
# Keycloak config
# ==========================================================
keycloak.ip=**************
keycloak.port=8443
keycloak.user=
keycloak.pwd=

# ==========================================================
# RabbitMQ server configuration
# Basic configuration needed to communicate with RabbitMQ.
# HTTP mode of communication will be utilized in this case.
# ==========================================================
spring.rabbitmq.addresses=**************:5671
spring.rabbitmq.username=
spring.rabbitmq.password=
spring.rabbitmq.ssl.enabled=true
spring.rabbitmq.ssl.algorithm=TLSv1.2
spring.rabbitmq.signalMessageAIOpsQueueName=signal-messages-aiops
spring.rabbitmq.signalMessageAIOps.prefetchCount=10
spring.rabbitmq.signalMessageAIOps.acknowledgementMode=AUTO
spring.rabbitmq.signalMessageAIOps.concurrentConsumerSize=1

# ==========================================================
# Redis Server Configuration
# ==========================================================
spring.redis.cluster.nodes=**************:7001,**************:7002,**************:7003,**************:7004,**************:7005,**************:7006
spring.redis.ssl=true
spring.redis.username=
spring.redis.password=
spring.redis.cluster.mode=true
spring.redis.max.idle.connections=500
spring.redis.min.idle.connections=300
spring.redis.max.total.connections=500
spring.redis.max.wait.secs=20
spring.redis.share.native.connection=false

# ===============================================================
# Local Cache Configuration
# ===============================================================
redis.cache.mode=0
topology.configuration.cache.max.size=5000
topology.configuration.cache.expire.interval.minutes=60
masterData.configuration.cache.max.size=5000
masterData.configuration.cache.expire.interval.minutes=60
account.configuration.cache.max.size=5000
account.configuration.cache.expire.interval.minutes=60
incident.profile.configuration.cache.max.size=5000
incident.profile.configuration.cache.expire.interval.minutes=60

# ==========================================================
# Opensearch Server Configuration
# ==========================================================
opensearch.connection.io.reactor.size=5
opensearch.batch.size=50
opensearch.batch.queue.max.size=10000
opensearch.data.push.schedule.interval=5
opensearch.data.push.schedule.initial.delay=2

# ==========================================================
# Thread Pool Configuration
# ==========================================================
thread.pool.queue.capacity=1000

# ==========================================================
# LLM Details
# ==========================================================
llm.url=http://192.168.14.12:8080/v1/chat/completions
llm.criteria.suppress.per.incident.min=0
llm.suppress.api.per.min=0
llm.rest.api.pusher.schedule.initial.delay=2
llm.rest.api.pusher.schedule.interval=30
llm.rest.api.pusher.max.queue.size=50

# ==========================================================
# LLM-IS TOMCAT Server Details
# ==========================================================
server.port=8899
server.ssl.key-store=classpath:appnomic-keystore.jks
server.ssl.key-store-type=jks
server.ssl.key-store-password=
server.ssl.trust.store=classpath:appnomic-truststore.jks
server.ssl.trust.store.password=
http.connect.timeout.sec=120
http.read.timeout.sec=120

# ==========================================================
# Health Metrics Details
# ==========================================================
health.metrics.update.interval.milliseconds=60000
health.metrics.log.interval.milliseconds=60000
management.endpoints.web.exposure.include=*
management.endpoints.jmx.exposure.include=*
management.endpoint.health.enabled=true
management.endpoints.web.base-path=/measure
management.server.port=11500
spring.jmx.enabled=true

# ==========================================================
# Others
# ==========================================================
fetch.os.closed.signals.interval.days=30
redis.delete.closed.signal.scheduler.interval.seconds=3600
redis.delete.closed.signal.scheduler.initial.delay.seconds=10
minimum.service.count=2
maximum.questionandanswer.os=3
prompt.message.system.role=You are a helpful AI assistant good at creating RCAs. The user will provide topology, forensics, and traces data and you will provide a RCA for the given incident.
http.blocked.methods=TRACE,TRACK
forensic.category=CPU
forensic.lookback.interval.minutes=600
forensic.lookahead.interval.minutes=10
forensic.query.enable=true
traces.query.enable=true
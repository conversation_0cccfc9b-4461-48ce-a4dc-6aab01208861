#if($rcaSignalTemplate.topology != "")
topology: $rcaSignalTemplate.topology
#end
incident:
#foreach($event in $rcaSignalTemplate.rcaAnomalyTemplates)
   #if($event.workload)
  - affectedKpi: Anomalous behaviour observed on Kpi $event.kpiName when api call $event.instanceName was made to the service $event.serviceName
   #else
  - affectedKpi: Anomalous behaviour observed on Kpi $event.kpiName in the service $event.serviceName running on instance $event.instanceName
   #end
   #if($event.categoryId != 'Config')
    description: Kpi Value is $event.kpiValue $event.operationType #if($event.operationType == "lesser than" || $event.operationType == "greater than")$event.thresholds.Lower #else$event.thresholds.Lower and $event.thresholds.Upper #end
   #else
    #set($splitVal = $event.kpiValue.split("@#!#\@"))
    #if($splitVal.size() == 5 && $splitVal[0] == 'Modified')
     description: Kpi value is $splitVal[0].toLowerCase() from $splitVal[1] to $splitVal[2] for $splitVal[3]
    #end
   #end
#end
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/heal-llm-integration-service.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/heal-llm-integration-service_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <totalSizeCap>100MB</totalSizeCap>
            <maxHistory>5</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/heal-llm-integration-service-core.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/heal-llm-integration-service-core_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <totalSizeCap>100MB</totalSizeCap>
            <maxHistory>3</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>

    </appender>

    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/heal-llm-integration-service-stats.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/heal-llm-integration-service-stats_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <totalSizeCap>100MB</totalSizeCap>
            <maxHistory>3</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.heal.integration.service" level="DEBUG" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.heal.integration.service.scheduler" level="TRACE" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <root level="ERROR">
        <appender-ref ref="CORE_FILE"/>
    </root>
</configuration>
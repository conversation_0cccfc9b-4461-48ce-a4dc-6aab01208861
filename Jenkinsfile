pipeline {
    agent { label 'Second_Slave' }
    tools {
        jdk 'openjdk-17-second'
        maven 'Maven_388'
    }
    environment {
        NEXUS_COMMON_CREDS = credentials('0981d455-e100-4f93-9faf-151ac7e29d8a')
        NEXUS_URL = 'http://*************:8081'
    }

    options {
        buildDiscarder(logRotator(numToKeepStr: '5'))
	    office365ConnectorWebhooks([[
                name: '<PERSON>',
                notifyBackToNormal: true,
                notifyFailure: true,
                notifySuccess: true,
                notifyUnstable: true,
                url: "https://healsoftwareai.webhook.office.com/webhookb2/78345e71-2972-44c4-a270-fbae82662bf1@55dca2af-e23a-4402-b9a6-8833b28a02dc/JenkinsCI/7958868126734afeb78edb01dafdcc05/6fed72e3-b7dd-422f-9075-e6d96468feb0"
            ]]
        )
    }

    parameters {
        gitParameter branchFilter: 'origin/(.*)', defaultValue: 'master', name: '<PERSON><PERSON><PERSON>', type: 'PT_BRANCH_TAG'
    }

    stages {
        stage('Checkout') {
            steps {
                script {
                    currentBuild.displayName = "#${BUILD_NUMBER}->${params.BRANCH}"
                    currentBuild.description = "Branch: ${params.BRANCH} is used for this build"
                }
                git branch: "${params.BRANCH}", url: 'https://<EMAIL>/appsone/llm-integration-service.git', credentialsId: "fd197b00-fd06-4632-a018-36134111e086"
            }

        }
        stage('Build') {
            steps {
                withSonarQubeEnv('sonarqube_40') {
                    sh 'mvn clean install sonar:sonar'
                }
            }
        }
        /* stage("Quality gate") {
            steps {
                waitForQualityGate abortPipeline: true
            }
            post {
                always {
                    emailext body: 'Please check quality gate for singal detector', subject: 'Quality Gate', to: '<EMAIL>'
                }
            }
        } */
        stage('Archive Builds') {
            steps {
		        sh "mv target/heal-llm-integration-service*.tar.gz heal-llm-integration-service.tar.gz"
                archiveArtifacts artifacts: 'heal-llm-integration-service.tar.gz', fingerprint: true
            }
        }
        stage('Docker build') {
            steps {
                sh "tar -xvf heal-llm-integration-service.tar.gz"
                script {
                    pom = readMavenPom file: 'pom.xml'
                    version = pom.version
                }
                echo "Building project in version: ${version}"
                sh "docker build -t heal-llm-integration-service:${version} ."
            }
        }
        stage('Publish Docker Image') {
            steps {
                sh "docker save heal-llm-integration-service:${version} > heal-llm-integration-service_${version}.tar"
                sh "curl -v -u ${NEXUS_COMMON_CREDS} --upload-file heal-llm-integration-service_${version}.tar ${NEXUS_URL}/nexus/repository/tls_docker_images/heal-llm-integration-service_${version}.tar"
                sh "echo heal-llm-integration-service_${version} > /tmp/heal-llm-integration-service_version"
            }
        }
        stage('Docker Vapt scan') {
            when {
            expression { params.BRANCH == 'develop' }
                 }
            steps {
                sh "docker scan heal-llm-integration-service:${version} > heal-llm-integration-service-scanreport.txt | echo"
                sh 'curl -v -u ${NEXUS_COMMON_CREDS} --upload-file heal-llm-integration-service-scanreport.txt ${NEXUS_URL}/nexus/repository/Image_scan_report/heal-llm-integration-service-scanreport.txt --progress-bar'
            }
             post {
                success {
                    emailext attachmentsPattern: 'heal-llm-integration-service-scanreport.txt', body: 'Hi Team,<br><br> Please find the attached VAPT report fyr.<br><br>Kindly close it during next release.<br><br>Regards<br> Heal', subject: 'VAPT Report for heal-llm-integration-service', to: '<EMAIL>'
                }
           }
        }
        stage('Cleanup') {
            steps {
                sh "docker rmi -f heal-llm-integration-service:${version}"
                cleanWs()
            }
        }
    }
}